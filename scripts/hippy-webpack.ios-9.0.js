const path = require('path');
const webpack = require('webpack');
const CaseSensitivePathsPlugin = require('case-sensitive-paths-webpack-plugin');
const pkg = require('../package.json');
// eslint-disable-next-line import/no-dynamic-require
const manifest = require(path.resolve('./dist/ios/vendor-manifest.json'));

const CopyPlugin = require('copy-webpack-plugin');


const platform = 'ios';

module.exports = {
    mode: 'production',
    bail: true,
    devtool: 'source-map',
    entry: {
        index: ['regenerator-runtime', path.resolve(pkg.main)],
    },
    output: {
        filename: `[name].${platform}.js`,
        path: path.resolve(`./dist/${platform}-9.0/`),
        globalObject: '(0, eval)("this")',
    },
    plugins: [
        new webpack.NamedModulesPlugin(),
        new webpack.DefinePlugin({
            'process.env.NODE_ENV': JSON.stringify('production'),
            __PLATFORM__: JSON.stringify(platform),
        }),
        new CaseSensitivePathsPlugin(),
        new webpack.DllReferencePlugin({
            context: process.cwd(),
            manifest,
        }),


        new CopyPlugin([
            {from: 'src/assets', to: './assets/'},
        ]),
    ],
    optimization: {
        minimize: false,
    },
    module: {
        rules: [
            {
                test: /\.tsx?$/,
                // exclude: /node_modules/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            "presets": [
                                '@babel/preset-typescript',
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            "plugins": [
                                ["@babel/plugin-proposal-decorators", {"legacy": true}],
                                "@babel/plugin-proposal-optional-chaining",
                                "@babel/plugin-proposal-nullish-coalescing-operator",
                                "@babel/proposal-class-properties",
                                "@babel/plugin-proposal-object-rest-spread",
                            ]
                        },

                    },
                    'unicode-loader',
                ]
            },
            {
                test: /\.(jsx?)$/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            plugins: [
                                '@babel/plugin-proposal-class-properties',
                            ],
                        },
                    },
                    'unicode-loader',
                ],
            },
            {
                test: /\.(png|jpg|gif)$/,
                use: [{
                    loader: 'file-loader',
                    options: {
                        name: '[name].[ext]',
                        outputPath: 'assets/',
                    },
                }],
            },
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
            '@hippy/react': path.resolve(__dirname, '../node_modules/@hippy/react'),
        },
    },
};
