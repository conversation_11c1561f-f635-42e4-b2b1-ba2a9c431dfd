const path = require('path');
const webpack = require('webpack');
const pkg = require('../package.json');
const CopyPlugin = require('copy-webpack-plugin');
//简易配置
const WebpackBar = require('webpackbar')

module.exports = {
    mode: 'development',
    devtool: 'cheap-module-eval-source-map',
    watch: true,
    watchOptions: {
        aggregateTimeout: 1500,
    },
    entry: {
        index: ['regenerator-runtime', path.resolve(pkg.main)],
    },
    output: {
        filename: 'index.bundle',
        strictModuleExceptionHandling: true,
        path: path.resolve('./dist/dev/'),
    },
    plugins: [
        new webpack.DefinePlugin({
            'process.env': {
                NODE_ENV: JSON.stringify('development'),
                HOST: JSON.stringify(process.env.DEV_HOST || '127.0.0.1'),
                PORT: JSON.stringify(process.env.DEV_PORT || 38989),
            },
            __PLATFORM__: null,
        }),

        new CopyPlugin([
            {from: 'src/assets', to: './assets/'},
        ]),
        new WebpackBar(),
    ],
    module: {
        rules: [

            // All files with a '.ts' or '.tsx' extension will be handled by 'ts-loader'.
            {
                test: /\.tsx?$/,
                // exclude: /node_modules/,
                use: [
                    {
                        loader: "babel-loader",
                        options: {
                            "presets": [
                                '@babel/preset-typescript',
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            "plugins": [
                                ["@babel/plugin-proposal-decorators", {"legacy": true}],
                                "@babel/proposal-class-properties",
                                "@babel/plugin-proposal-optional-chaining",
                                "@babel/plugin-proposal-nullish-coalescing-operator",
                            ],
                        },

                    },
                    'unicode-loader',
                ]
            },
            {
                test: /\.(jsx?)$/,
                use: [
                    {
                        loader: 'babel-loader',
                        options: {
                            presets: [
                                '@babel/preset-react',
                                [
                                    '@babel/preset-env',
                                    {
                                        targets: {
                                            ios: 9,
                                        },
                                    },
                                ],
                            ],
                            plugins: [
                                '@babel/plugin-proposal-class-properties',
                            ],
                        },
                    },
                    'unicode-loader',
                ],
            },
            {
                test: /\.json$/,
                loader: 'json-loader',
            },

            // {
            //     test: /\.tsx?$/,
            //     exclude: /node_modules/,
            //     use: [
            //         {
            //             loader: 'awesome-typescript-loader'
            //         }
            //     ]
            // },
            // All output '.js' files will have any sourcemaps re-processed by 'src-map-loader'.
            // {enforce: "pre", test: /\.js$/, loader: "src-map-loader"}
        ],
    },
    resolve: {
        extensions: ['.js', '.jsx', '.json', '.ts', '.tsx'],
        modules: [path.resolve(__dirname, '../node_modules')],
        alias: {
            '@hippy/react': path.resolve(__dirname, '../node_modules/@hippy/react'),
        },
    },
};
