const path = require('path');
const webpack = require('webpack');
const WebpackAliOSSPlugin = require('webpack-oss')

const buildEvn = process.env.BUILD_ENV;
console.log("buildEvn = " + buildEvn);

let ossHippyPrefix = "";
let ossPrefix ="";
if (buildEvn === "dev" || buildEvn === "test") {
    ossHippyPrefix = "hippy_test";
    ossPrefix = "test";
} else if (buildEvn === "gray") {
    ossHippyPrefix = "hippy_gray";
    ossPrefix = "gray";
} else if (buildEvn === "prod") {
    ossHippyPrefix = "hippy_release";
    ossPrefix = "release";
}else if (buildEvn === "pre") {
    ossHippyPrefix = "hippy_pre";
    ossPrefix = "pre";
}



const accessKeyId = 'LTAI5t8jWB7k484hkfNg5y9r';
const accessKeySecret = '******************************';
module.exports = {
    mode: 'production',
    bail: true,
    entry: {
        vendor: [path.resolve(__dirname, './vendor.js')],
    },
    output: {
        library: 'hippyReactBase',
    },
    plugins: [
        new webpack.NamedModulesPlugin(),
        new WebpackAliOSSPlugin( {
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            region: "oss-cn-shanghai",
            bucket: "cis-static-common",
            prefix:"apks/plugins/" + ossHippyPrefix,
            exclude: [ {
                test:function(name) {
                    return name.indexOf(".zip") < 0;
                }
            } ], // 或者 /.*\.html$/,排除.html文件的上传
            deleteAll: false,	  // 优先匹配format配置项
            local: true,   // 上传打包输出目录里的文件,
            output: path.resolve(__dirname, '../dist/')
        } ),

        new WebpackAliOSSPlugin( {
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            region: "oss-cn-shanghai",
            bucket: "cis-static-common",
            prefix:`apks/abcyun-clinic-app/hippy-jsbundle-source-map/${ossPrefix}/android/`,
            exclude: [ {
                test:function(name) {
                    return name.indexOf(".zip") < 0;
                }
            } ], // 或者 /.*\.html$/,排除.html文件的上传
            deleteAll: false,	  // 优先匹配format配置项
            local: true,   // 上传打包输出目录里的文件,
            output: path.resolve(__dirname, '../dist/source_map/android')
        }),
        new WebpackAliOSSPlugin( {
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            region: "oss-cn-shanghai",
            bucket: "cis-static-common",
            prefix:`apks/abcyun-clinic-app/hippy-jsbundle-source-map/${ossPrefix}/ios/`,
            exclude: [ {
                test:function(name) {
                    return name.indexOf(".zip") < 0;
                }
            } ], // 或者 /.*\.html$/,排除.html文件的上传
            deleteAll: false,	  // 优先匹配format配置项
            local: true,   // 上传打包输出目录里的文件,
            output: path.resolve(__dirname, '../dist/source_map/ios')
        }),
        new WebpackAliOSSPlugin( {
            accessKeyId: accessKeyId,
            accessKeySecret: accessKeySecret,
            region: "oss-cn-shanghai",
            bucket: "cis-static-common",
            prefix:`apks/abcyun-clinic-app/hippy-jsbundle-source-map/${ossPrefix}/ios-9.0/`,
            exclude: [ {
                test:function(name) {
                    return name.indexOf(".zip") < 0;
                }
            } ], // 或者 /.*\.html$/,排除.html文件的上传
            deleteAll: false,	  // 优先匹配format配置项
            local: true,   // 上传打包输出目录里的文件,
            output: path.resolve(__dirname, '../dist/source_map/ios')
        })
    ]
};
