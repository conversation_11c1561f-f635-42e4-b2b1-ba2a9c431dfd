# coding:utf-8
import re
import json

# 输入文件名
input_file = "./tools/iconfont/iconfont.js"

# 读取文件内容
with open(input_file, "r") as f:
    content = f.read()

# 提取 SVG 部分内容
match = re.search(r"'(<svg>.*?</svg>)'", content, re.DOTALL)
if not match:
    print("未找到 SVG 内容")
    exit(1)

svg_content = match.group(1)

# 提取所有 <symbol>
symbol_pattern = re.compile(r'<symbol\s+id="([^"]+)"[^>]*>(.*?)</symbol>', re.DOTALL)
symbols = symbol_pattern.findall(svg_content)

# 构建 JSON 对象
symbol_dict = {}
for symbol_id, symbol_body in symbols:
    symbol_dict[symbol_id] = symbol_body

# 写入 JSON 文件
with open("./src/sub-project/base-ui/iconfont/icons.ts", "w") as f:
    f.write("/* eslint-disable */ \n const icons:{ [key: string]: string } = " + json.dumps(symbol_dict, indent=4, ensure_ascii=False) + "\n export default icons")

# 同步写入 abc-mobile-ui 组件库 icons.ts
with open("./packages/abc-mobile-ui/src/components/abc-iconfont/icons.ts", "w") as f:
    f.write("/* eslint-disable */ \n const icons:{ [key: string]: string } = " + json.dumps(symbol_dict, indent=4, ensure_ascii=False) + "\n export default icons")

print("已生成 icons.ts，并同步到 abc-mobile-ui icons.ts")
