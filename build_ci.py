#!/usr/bin/env python
# -*- coding: UTF-8 -*-

import sys
import os
import hashlib
import json
import time
import requests

# 用于生成source map压缩包的加密密码
sourceMapPassword = 'r5Y!xqT4aq%TK#yh'
outputDir = 'dist'
timestamp = time.strftime('%Y%m%d_%H%M%S', time.localtime(time.time()))

env_dist = os.environ

buildNum = str(int(time.time()/60))
buildEnv = env_dist['BUILD_ENV']
tagName = env_dist['TAG_NAME']
envExport = 'export BUILD_NUMBER=' + buildNum + " && export BUILD_ENV=" + buildEnv

deployRegion = env_dist.get('DEPLOY_REGION')
if deployRegion is None:
    deployRegion = ""

if deployRegion == "" or deployRegion == "default":
    deployRegion = ""
else:
    deployRegion = deployRegion

buildOS = env_dist.get('BUILD_OS')
buildIOS = False
buildIOS9 = False
buildAndroid = False
if buildOS is None:
    buildIOS = True
    buildIOS9 = True
    buildAndroid = True
elif buildIOS == 'IOS':
    buildIOS = True
elif buildIOS == 'Android':
    buildAndroid = True
elif buildIOS == 'IOS9':
    buildIOS9 = True


apiUrlPrefix = ""
if buildEnv == "dev":
    apiUrlPrefix = "dev.rpc.abczs.cn"
elif buildEnv == "test":
    apiUrlPrefix = "test.rpc.abczs.cn"
elif buildEnv == "prod" or buildEnv == "gray" or buildEnv == "pre":
    apiUrlPrefix = "pre.rpc.abczs.cn"

apiUrlPrefix = "http://" + apiUrlPrefix + "/rpc/mobile/plugin/updatePlugin"


print("apiUrlPrefix = " + apiUrlPrefix)
print("deployRegion = " + deployRegion)
print("buildNum =" + buildNum)
print("tagName =" + tagName)


class PluginInfo:
    def __init__(self):
        self.name = ''
        self.version = ''
        self.md5 = ''
        self.name = ''
        self.host_min_version = ''
        self.host_max_version = ''
        self.url = ''
        self.region = ''


class PackageInfo:
    def __init__(self):
        self.hostMinVersion = ''
        self.hostMaxVersion = ''
        self.version = ''
        self.name = ''
        self.targetPlatform = ''
        self.osVersion = ''

    def loadFromJson(self, packageJson):
        self.hostMinVersion = packageJson['hostMinVersion']
        self.hostMaxVersion = packageJson['hostMaxVersion']
        self.version = packageJson['version']
        self.name = packageJson['name']



def _loadPackageInfo():
    with open("./package.json", "r") as load_f:
        packageJson = json.load(load_f)
        packageInfo = PackageInfo()
        packageInfo.loadFromJson(packageJson)
        return packageInfo


def _prepareEnv():
    initDirsCmd = 'rm -rf {0}'.format(outputDir)
    os.system(initDirsCmd)

    print('删除node_modules目录')
    rmNodeModules = 'rm -rf node_modules'
    os.system(rmNodeModules)

    print('node version:')
    os.system('node --version')
    print('npm version:')
    os.system('npm --version')
    buildVendorCmd = 'npm i'
    if os.system(buildVendorCmd) != 0:
        print('npm i 失败')
        raise Exception("npm i 失败")

    buildVendorCmd = 'npm run tsc'
    if os.system(buildVendorCmd) != 0:
        print('npm run tsc')
        raise Exception("npm run tsc 语法检测失败")

    buildVendorCmd = 'npm run hippy:vendor'
    if os.system(buildVendorCmd) != 0:
        print('build hippy:vendor失败')
        raise Exception("build hippy:vendor失败")

    buildCmd = 'npm run hippy:build'
    if os.system(buildCmd) != 0:
        print('build hippy:build失败')
        raise Exception("build hippy:build失败")


def _build():
    os.system('mkdir dist/source_map')
    if buildIOS:
        os.system('mkdir dist/source_map/ios')
        os.system('mv dist/ios/index.ios.js.map dist/source_map/ios')
    if buildAndroid:
        os.system('mkdir dist/source_map/android')
        os.system('mv dist/android/index.android.js.map dist/source_map/android')
    if buildIOS9:
        os.system('mkdir dist/source_map/ios-9.0')
        os.system('mv dist/ios-9.0/index.ios.js.map dist/source_map/ios-9.0')

    return True


def _ossUpload():
    buildCmd = envExport + ' && npm run ossZipUpload'
    print("_ossUpload cmd = " + buildCmd)
    if os.system(buildCmd) != 0:
        print('ossZipUpload 失败')
        raise Exception("OSS 上传失败")


def _fileMD5(file):
    f = open(file, 'rb')
    md5_obj = hashlib.md5()
    while True:
        d = f.read(8096)
        if not d:
            break
        md5_obj.update(d)
    hash_code = md5_obj.hexdigest()
    f.close()
    md5 = str(hash_code).lower()
    return md5


def _writeConfFile(file, packageInfo):
    with open(file, "w") as f:
        jsonConf = {}
        jsonConf['hostMinVersion'] = packageInfo.hostMinVersion
        jsonConf['hostMaxVersion'] = packageInfo.hostMaxVersion
        jsonConf['name'] = packageInfo.name
        jsonConf['version'] = packageInfo.version
        jsonConf['timestamp'] = timestamp
        jsonConf['repoTag'] = tagName
        jsonConf['buildEnv'] = buildEnv
        json.dump(jsonConf, f)


def _writeZipInfo(infoFile, zipFile, packageInfo):
    pluginInfo = {}
    pluginInfo['name'] = packageInfo.name
    pluginInfo['version'] = packageInfo.version
    pluginInfo['md5'] = _fileMD5(zipFile)
    pluginInfo['hostMinVersion'] = packageInfo.hostMinVersion
    pluginInfo['hostMaxVersion'] = packageInfo.hostMaxVersion
    if deployRegion != "":
        pluginInfo['region'] = deployRegion
    pluginInfo['appId'] = "cn.abcyun.clinic.app"
    pluginInfo['targetPlatform'] = packageInfo.targetPlatform
    if packageInfo.osVersion != "":
        pluginInfo['osVersion'] = packageInfo.osVersion

    # 测试+开发配置
    pluginInfo[
        'url'] = "https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/plugins/hippy_test/" + os.path.basename(
        zipFile)

    with open(infoFile, "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))

    with open(infoFile + ".for_curl", "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))

    # 预发布配置
    pluginInfo[
        'url'] = "https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/plugins/hippy_pre/" + os.path.basename(
        zipFile)
    with open(infoFile + ".pre_for_curl", "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))

    # 灰度配置
    pluginInfo[
        'url'] = "https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/plugins/hippy_gray/" + os.path.basename(
        zipFile)
    with open(infoFile + ".gray_for_curl", "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))

    # 正式配置
    pluginInfo[
        'url'] = "https://cis-static-common.oss-cn-shanghai.aliyuncs.com/apks/plugins/hippy_release/" + os.path.basename(
        zipFile)
    with open(infoFile + ".release_for_curl", "w") as f:
        f.write(json.dumps(pluginInfo, sort_keys=True, indent=4, separators=(',', ': ')))


def _generateZipPlugins(packageInfo):
    zipNameSuffix = "{0}_{1}_{2}_{3}.zip".format(packageInfo.name, packageInfo.version, deployRegion, timestamp)
    if buildAndroid:
        _writeConfFile('dist/android/conf.json', packageInfo)
        cmd = 'cd dist/android && zip -q -r -o  ../android.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newAndroidZipName = outputDir + '/android_{0}'.format(zipNameSuffix)
        mvAndroidZipCmd = 'mv ' + outputDir + '/android.zip  ' + newAndroidZipName
        print("mvAndroidZipCmd = " + mvAndroidZipCmd)
        os.system(mvAndroidZipCmd)
        packageInfo.targetPlatform = '2'
        print("packageInfo.android_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        _writeZipInfo("./dist/android_zip_info.txt", newAndroidZipName, packageInfo)

    if buildIOS:
        _writeConfFile('dist/ios/conf.json', packageInfo)
        cmd = 'cd dist/ios && zip -q -r -o  ../ios.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newIOSZipName = outputDir + '/ios_{0}'.format(zipNameSuffix)
        mvIOSZipCmd = 'mv ' + outputDir + '/ios.zip  ' + newIOSZipName
        os.system(mvIOSZipCmd)
        packageInfo.targetPlatform = '1'
        packageInfo.osVersion = '10.0.0-'
        print("packageInfo.ios_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        print("packageInfo.ios_zip_info.osVersion = " + packageInfo.osVersion)
        _writeZipInfo("./dist/ios_zip_info.txt", newIOSZipName, packageInfo)

    if buildIOS9:
        _writeConfFile('dist/ios-9.0/conf.json', packageInfo)
        cmd = 'cd dist/ios-9.0 && zip -q -r -o  ../ios-9.0.zip .'
        if os.system(cmd) != 0:
            print('生成zip包失败')
            return False

        newIOSZipName = outputDir + '/ios_9.0_{0}'.format(zipNameSuffix)
        mvIOSZipCmd = 'mv ' + outputDir + '/ios-9.0.zip  ' + newIOSZipName
        os.system(mvIOSZipCmd)
        packageInfo.osVersion = '-9.9.9'
        print("packageInfo.0_zip_info.targetPlatform = " + packageInfo.targetPlatform)
        print("packageInfo.0_zip_info.osVersion = " + packageInfo.osVersion)
        _writeZipInfo("./dist/ios_9.0_zip_info.txt", newIOSZipName, packageInfo)

    return True

def _generateSourceMapZip(packageInfo):
    if buildAndroid:
        cmd = 'cd dist/source_map/android && zip -q -r -o  --password ' + sourceMapPassword + ' '+ packageInfo.version + '.zip index.android.js.map'
        os.system(cmd)

    if buildIOS:
        cmd = 'cd dist/source_map/ios && zip -q -r -o  --password ' + sourceMapPassword + ' '+ packageInfo.version + '.zip index.ios.js.map'
        os.system(cmd)

    if buildIOS9:
        cmd = 'cd dist/source_map/ios-9.0 && zip -q -r -o  --password ' + sourceMapPassword + ' '+ packageInfo.version + '.zip index.ios.js.map'
        os.system(cmd)

    return True


def _generateEmbedsBundle(packageInfo):
    cmd = 'cd dist && mkdir for_embeded && mkdir for_embeded/ios && mkdir for_embeded/ios-9.0 && mkdir for_embeded/android'
    if os.system(cmd) != 0:
        raise Exception("生成内置包失败")

    if buildIOS:
        cmd = 'cd dist && cp -rf ios  for_embeded/ios/' + packageInfo.name
        if os.system(cmd) != 0:
            raise Exception("生成内置包失败")

    if buildIOS9:
        cmd = 'cd dist && cp -rf ios-9.0  for_embeded/ios-9.0/' + packageInfo.name
        if os.system(cmd) != 0:
            raise Exception("生成内置包失败")

    if buildAndroid:
        cmd = 'cd dist && cp -rf android  for_embeded/android/' + packageInfo.name
        if os.system(cmd) != 0:
            raise Exception("生成内置包失败")


def _updateUpgradeItem(url, file, grayFlag):
    print("_updateUpgradeItem url = " + url + ", file = " + file)
    f = None
    content = None
    with open(file, 'r') as f:
        content = json.load(f)
        content['grayFlag'] = grayFlag
        content = json.dumps(content)

    print("_updateUpgradeItem url = " + url + ", content = " + content)

    headers = {"content-type": "application/json"}
    r = requests.put(url, data=content,headers=headers)
    print("_updateUpgradeItem rsp code:" + str(r.status_code))
    print("_updateUpgradeItem, rsp content = " + r.content)
    if r.status_code != 200:
        raise Exception("_updateUpgradeItem 更改配置失败")


def _updateUpgradeInfo():
    if buildEnv == "dev" or buildEnv == "test":
        if buildAndroid:
            _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt.for_curl", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt.for_curl", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt.for_curl", "2")
        if buildIOS:
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt.for_curl", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt.for_curl", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt.for_curl", "2")
        if buildIOS9:
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt.for_curl", "0")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt.for_curl", "1")
            _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt.for_curl", "2")
    elif buildEnv == "gray":
        _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt.gray_for_curl", "1")
        _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt.gray_for_curl", "1")
        _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt.gray_for_curl", "1")
    elif buildEnv == "prod":
        _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt.release_for_curl", "0")
        _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt.release_for_curl", "0")
        _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt.release_for_curl", "0")
    elif buildEnv == "pre":
        _updateUpgradeItem(apiUrlPrefix, "./dist/android_zip_info.txt.pre_for_curl", "2")
        _updateUpgradeItem(apiUrlPrefix, "./dist/ios_zip_info.txt.pre_for_curl", "2")
        _updateUpgradeItem(apiUrlPrefix, "./dist/ios_9.0_zip_info.txt.pre_for_curl", "2")


packageInfo = _loadPackageInfo()
packageInfo.version += '.' + buildNum

print('version = ' + packageInfo.version)


def main():
    reload(sys)
    sys.setdefaultencoding('utf8')
    _prepareEnv()
    _build()
    _generateZipPlugins(packageInfo)
    _generateSourceMapZip(packageInfo)
    _generateEmbedsBundle(packageInfo)
    _ossUpload()
    _updateUpgradeInfo()


if __name__ == '__main__':
    main()
