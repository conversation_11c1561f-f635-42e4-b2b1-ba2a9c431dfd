interface Event {
    type: string;
    bubbles: boolean;
    cancelable: boolean;
    timeStamp: Date | number;
    currentTarget: Element | null;
    target: Element | null;
}
declare class Event implements Event {
    /**
     * constructor
     * @param eventName - handler name, e.g. onClick
     * @param currentTarget - currentTarget is the node which the handler bind to
     * @param target - target is the node which triggered the real event
     */
    constructor(eventName: string, currentTarget: Element | null, target: Element | null);
    stopPropagation(): void;
    preventDefault(): void;
}
export default Event;
