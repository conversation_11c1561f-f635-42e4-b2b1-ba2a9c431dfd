interface HippyEventHub {
    eventName: string;
    nextIdForHandler: number;
    handlerContainer: {
        [key: string]: {
            id: number;
            eventHandler: Function;
            context: any;
        } | undefined;
    };
}
declare class HippyEventHub implements HippyEventHub {
    constructor(eventName: string);
    getEventListeners(): ({
        id: number;
        eventHandler: Function;
        context: any;
    } | undefined)[];
    getHandlerSize(): number;
    addEventHandler(handler: Function, callContext: any): number;
    notifyEvent(eventParams: any): void;
    removeEventHandler(handlerId: number | undefined): void;
}
export default HippyEventHub;
