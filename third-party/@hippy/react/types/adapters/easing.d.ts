declare const Easing: {
    step0(n: number): number;
    step1(n: number): number;
    linear(): string;
    ease(): string;
    quad(t: number): number;
    cubic(t: number): number;
    poly(n: number): (t: number) => number;
    sin(t: number): number;
    circle(t: number): number;
    exp(t: number): number;
    elastic(): string;
    back(s?: number): (t: number) => number;
    bounce(t_: number): number;
    bezier(): string;
    in(): string;
    out(): string;
    inOut(): string;
};
export default Easing;
