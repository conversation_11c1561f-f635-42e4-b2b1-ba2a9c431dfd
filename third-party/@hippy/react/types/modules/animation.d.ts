import { HippyEventRevoker } from '../event';
declare type AnimationValue = number | {
    animationId: number;
} | string;
declare type AnimationCallback = () => void;
declare type AnimationDirection = 'left' | 'right' | 'top' | 'bottom' | 'center';
interface AnimationOptions {
    /**
     * Initial value at `Animation` start
     */
    startValue: AnimationValue;
    /**
     * End value when `Animation` end.
     */
    toValue: AnimationValue;
    /**
     * Animation execution time
     */
    duration: number;
    /**
     * Timeline mode of animation
     */
    mode?: 'timing';
    /**
     * Delay starting time
     */
    delay?: number;
    /**
     * Value type, leave it blank in most case, except use rotate/color related
     * animation, set it to be 'deg' or 'color'.
     */
    valueType?: 'deg' | 'color';
    /**
     * Animation start position
     */
    direction?: AnimationDirection;
    /**
     * Animation interpolation type
     */
    timingFunction?: 'linear' | 'ease' | 'bezier' | 'in' | 'ease-in' | 'out' | 'ease-out' | 'inOut' | 'ease-in-out' | (string & {});
    /**
     * Animation repeat times, use 'loop' to be always repeating.
     */
    repeatCount?: number | 'loop';
    inputRange?: any[];
    outputRange?: any[];
}
interface Animation extends AnimationOptions {
    animationId: number;
    onAnimationStartCallback?: AnimationCallback;
    onAnimationEndCallback?: AnimationCallback;
    onAnimationCancelCallback?: AnimationCallback;
    onAnimationRepeatCallback?: AnimationCallback;
    animationStartListener?: HippyEventRevoker;
    animationEndListener?: HippyEventRevoker;
    animationCancelListener?: HippyEventRevoker;
    animationRepeatListener?: HippyEventRevoker;
    onRNfqbAnimationStart?: Function;
    onRNfqbAnimationEnd?: Function;
    onRNfqbAnimationCancel?: Function;
    onRNfqbAnimationRepeat?: Function;
    onHippyAnimationStart?: Function;
    onHippyAnimationEnd?: Function;
    onHippyAnimationCancel?: Function;
    onHippyAnimationRepeat?: Function;
}
/**
 * Better performance of Animation solution.
 *
 * It pushes the animation scheme to native at once.
 */
declare class Animation implements Animation {
    constructor(config: AnimationOptions);
    /**
     * Remove all of animation event listener
     */
    removeEventListener(): void;
    /**
     * Start animation execution
     */
    start(): void;
    /**
     * Use destroy() to destroy animation.
     */
    destory(): void;
    /**
     * Destroy the animation
     */
    destroy(): void;
    /**
     * Pause the running animation
     */
    pause(): void;
    /**
     * Resume execution of paused animation
     */
    resume(): void;
    /**
     * Update to new animation scheme
     *
     * @param {Object} newConfig - new animation schema
     */
    updateAnimation(newConfig: AnimationOptions): void;
    /**
     * Call when animation started.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationStart(cb: AnimationCallback): void;
    /**
     * Call when animation is ended.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationEnd(cb: AnimationCallback): void;
    /**
     * Call when animation is canceled.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationCancel(cb: AnimationCallback): void;
    /**
     * Call when animation is repeated.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationRepeat(cb: AnimationCallback): void;
}
export default Animation;
export { Animation, AnimationCallback, };
