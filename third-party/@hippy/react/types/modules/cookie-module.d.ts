/**
 * Get cookies from url
 *
 * @param {string} url - Specific url for cookie
 */
declare function getCookies(url: string): Promise<string>;
/**
 * Set cookie to url
 *
 * @param {string} url - Specific url for cookie.
 * @param {string} keyValue - Cookie key and value string, split with `:`.
 * @param {Date|string} [expires] - UTC Date string or Date object for cookie expire.
 */
declare function setCookie(url: string, keyValue: string, expires: string | Date): void;
export { getCookies, setCookie, };
