declare type NetworkChangeEventData = any;
declare type NetworkInfoCallback = (data: NetworkChangeEventData) => void;
interface NetInfoRevoker {
    eventName: string;
    listener?: NetworkInfoCallback | undefined;
}
declare class NetInfoRevoker implements NetInfoRevoker {
    constructor(eventName: string, listener: (data: any) => void);
    remove(): void;
}
/**
 * Add a network status event listener
 *
 * @param {string} eventName - Event name will listen for NetInfo module,
 *                             use `change` for listen network change.
 * @param {function} listener - Event status event callback
 * @returns {object} NetInfoRevoker - The event revoker for destroy the network info event listener.
 */
declare function addEventListener(eventName: string, listener: NetworkInfoCallback): NetInfoRevoker;
/**
 * Remove network status event event listener
 *
 * @param {string} eventName - Event name will listen for NetInfo module,
 *                             use `change` for listen network change.
 * @param {Function} [listener] - The specific event listener will remove.
 */
declare function removeEventListener(eventName: string, listener?: NetInfoRevoker | NetworkInfoCallback): void;
/**
 * Get the current network status
 */
declare function fetch(): Promise<NetworkChangeEventData>;
export { addEventListener, removeEventListener, fetch, };
