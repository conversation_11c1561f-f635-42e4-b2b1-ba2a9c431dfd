import { Animation, AnimationCallback } from './animation';
import '../global';
interface AnimationInstance {
    animationId: number;
    follow: boolean;
}
interface AnimationChild {
    animation: Animation;
    follow: boolean;
}
interface AnimationSetOption {
    children: AnimationChild[];
    repeatCount: number;
    virtual?: any;
}
interface AnimationSet extends Animation {
    animationId: number;
    animationList: AnimationInstance[];
    onRNfqbAnimationStart?: Function;
    onRNfqbAnimationEnd?: Function;
    onRNfqbAnimationCancel?: Function;
    onRNfqbAnimationRepeat?: Function;
    onHippyAnimationStart?: Function;
    onHippyAnimationEnd?: Function;
    onHippyAnimationCancel?: Function;
    onHippyAnimationRepeat?: Function;
}
/**
 * Better performance of Animation series solution.
 *
 * It pushes the animation scheme to native at once.
 */
declare class AnimationSet implements AnimationSet {
    constructor(config: AnimationSetOption);
    /**
     * Remove all of animation event listener
     */
    removeEventListener(): void;
    /**
     * Start animation execution
     */
    start(): void;
    /**
     * Use destroy() to destroy animation.
     */
    destory(): void;
    /**
     * Destroy the animation
     */
    destroy(): void;
    /**
     * Pause the running animation
     */
    pause(): void;
    /**
     * Resume execution of paused animation
     */
    resume(): void;
    /**
     * Call when animation started.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationStart(cb: AnimationCallback): void;
    /**
     * Call when animation is ended.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationEnd(cb: AnimationCallback): void;
    /**
     * Call when animation is canceled.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationCancel(cb: AnimationCallback): void;
    /**
     * Call when animation is repeated.
     * @param {Function} cb - callback when animation started.
     */
    onAnimationRepeat(cb: AnimationCallback): void;
}
export default AnimationSet;
