declare type Color = string | number;
interface ColorParserOption {
    platform?: HippyTypes.Platform;
}
/**
 * Parse the color value to integer that native understand.
 *
 * @param {string} color - The color value.
 * @param {object} options - Color options.
 */
declare function colorParse(color: Color, options?: ColorParserOption): Color;
/**
 * Parse the color values array to integer array that native understand.
 *
 * @param {string[]} colorArray The color values array.
 * @param {object} options Color options.
 */
declare function colorArrayParse(colorArray: Color[], options?: ColorParserOption): Color[];
export { Color, colorParse, colorArrayParse, };
