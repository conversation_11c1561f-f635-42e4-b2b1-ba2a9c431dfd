import React from 'react';
import { LayoutableProps } from '../types';
interface CollapsePullHeaderOptions {
    time?: number;
}
interface PullHeaderProps extends LayoutableProps {
    /**
     * Trigger when release the finger after pulling distance larger than the content height
     */
    onHeaderReleased?: () => void;
    /**
     * Trigger when pulling
     *
     * @param {Object} evt - Event data
     * @param {number} evt.contentOffset - Dragging distance
     */
    onHeaderPulling?: (evt: HippyTypes.PullingEvent) => void;
}
declare class PullHeader extends React.Component<PullHeaderProps, {}> {
    private instance;
    /**
     * Expand the PullView and display the content
     */
    expandPullHeader(): void;
    /**
     * Collapse the PullView and hide the content
     * @param {CollapsePullHeaderOptions} [options] - additional config for pull header
     */
    collapsePullHeader(options: CollapsePullHeaderOptions): void;
    render(): JSX.Element;
}
export default PullHeader;
