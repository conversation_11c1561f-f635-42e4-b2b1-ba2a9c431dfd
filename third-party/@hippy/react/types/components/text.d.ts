import React from 'react';
import { LayoutableProps, ClickableProps } from '../types';
interface TextProps extends LayoutableProps, ClickableProps {
    /**
     * Used to truncate the text with an ellipsis after computing the text layout,
     * including line wrapping, such that the total number of lines does not exceed this number.
     * This prop is commonly used with `ellipsizeMode`.
     */
    numberOfLines?: number;
    /**
     * Determines what the opacity of the wrapped view.
     */
    opacity?: number;
    /**
     * When numberOfLines is set, this prop defines how text will be truncated.
     * numberOfLines must be set in conjunction with this prop.
     * This can be one of the following values:
     *
     * * head - The line is displayed so that the end fits in the container
     *          and the missing text at the beginning of the line is indicated by an ellipsis glyph.
     *          e.g., "...wxyz
     * * middle - The line is displayed so that the beginning and
     *            end fit in the container and the missing text in the middle is indicated
     *            by an ellipsis glyph.
     *            e.g., "ab...yz"
     * * tail - The line is displayed so that the beginning fits in the container
     *          and the missing text at the end of the line is indicated by an ellipsis glyph.
     *          e.g., "abcd..."
     * * clip - Lines are not drawn past the edge of the text container.
     *
     * The default is `tail`.
     */
    ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
    children: number | string | string[] | any;
    text?: string;
    style?: HippyTypes.Style | HippyTypes.Style[];
}
declare const Text: React.ForwardRefExoticComponent<TextProps & React.RefAttributes<HTMLParagraphElement>>;
export default Text;
