import React from 'react';
interface ScrollEvent {
    contentInset: {
        right: number;
        top: number;
        left: number;
        bottom: number;
    };
    layoutMeasurement: {
        width: number;
        height: number;
    };
    contentSize: {
        width: number;
        height: number;
    };
    zoomScale?: number;
    contentOffset: {
        x: number;
        y: number;
    };
}
interface ScrollViewProps {
    /**
     * Overrides the text that's read by the screen reader when the user interacts with the element.
     * By default, the label is constructed by traversing all the children and accumulating
     * all the Text nodes separated by space.
     */
    accessibilityLabel?: string;
    /**
     * When `true`, indicates that the view is an accessibility element.
     * By default, all the touchable elements are accessible.
     */
    accessible?: boolean;

    style?: HippyTypes.Style;
    /**
     * When true, the scroll view's children are arranged horizontally in a row
     * instead of vertically in a column.
     * The default value is `false`.
     */
    horizontal?: boolean;
    /**
     * When `true`, the scroll view stops on multiples of the scroll view's size when scrolling.
     * This can be used for horizontal pagination.
     * Default: false
     */
    pagingEnabled?: boolean;
    /**
     * When `false`, the view cannot be scrolled via touch interaction.
     * Default: true
     *
     * > Note that the view can always be scrolled by calling scrollTo.
     */
    scrollEnabled?: boolean;
    /**
     * When `true`, shows a horizontal scroll indicator.
     * Default: true
     */
    showsHorizontalScrollIndicator?: boolean;
    /**
     * When `true`, shows a vertical scroll indicator.
     * Default: true
     */
    showsVerticalScrollIndicator?: boolean;
    /**
     * These styles will be applied to the scroll view content container which wraps all
     * of the child views.
     */
    contentContainerStyle?: HippyTypes.Style;
    /**
     * This controls how often the scroll event will be fired while scrolling
     * (as a time interval in ms). A lower number yields better accuracy for code
     * that is tracking the scroll position, but can lead to scroll performance
     * problems due to the volume of information being send over the bridge.
     * You will not notice a difference between values set between 1-16 as the JS run loop
     * is synced to the screen refresh rate. If you do not need precise scroll position tracking,
     * set this value higher to limit the information being sent across the bridge.
     *
     * The default value is zero, which results in the scroll event being sent only once
     * each time the view is scrolled.
     */
    scrollEventThrottle?: number;
    /**
     * The amount by which the scroll view indicators are inset from the edges of the scroll view.
     * This should normally be set to the same value as the `contentInset`.
     *
     * Default: {top: 0, right: 0, bottom: 0, left: 0}.
     */
    scrollIndicatorInsets?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
    };
    /**
     * Called when the momentum scroll starts (scroll which occurs as the ScrollView starts gliding).
     */
    onMomentumScrollBegin?: (evt: ScrollEvent) => void;
    /**
     * Called when the momentum scroll ends (scroll which occurs as the ScrollView glides to a stop).
     */
    onMomentumScrollEnd?: (evt: ScrollEvent) => void;
    /**
     * Fires at most once per frame during scrolling.
     * The frequency of the events can be controlled using the `scrollEventThrottle` prop.
     *
     * @param {Object} evt - Scroll event data.
     * @param {number} evt.contentOffset.x - Offset X of scrolling.
     * @param {number} evt.contentOffset.y - Offset Y of scrolling.
     */
    onScroll?: (evt: ScrollEvent) => void;
    /**
     * Called when the user begins to drag the scroll view.
     */
    onScrollBeginDrag?: (evt: ScrollEvent) => void;
    /**
     * Called when the user stops dragging the scroll view and it either stops or begins to glide.
     */
    onScrollEndDrag?: (evt: ScrollEvent) => void;
    onLayout?: (evt: HippyTypes.LayoutEvent) => void;
    /**
     * called when content size changed
     * @param evt
     */
    onContentSizeChanged?: (evt: {
        width: number;
        height: number;
    }) => void;
}
/**
 * Scrollable View without recycle feature.
 *
 * If you need to implement a long list, use `ListView`.
 * @noInheritDoc
 */
declare class ScrollView extends React.Component<ScrollViewProps, {}> {
    private instance;
    /**
     * Scrolls to a given x, y offset, either immediately, with a smooth animation.
     *
     * @param {number} x - Scroll to horizon position X.
     * @param {number} y - Scroll To veritical position Y.
     * @param {boolean} animated - With smooth animation.By default is true.
     */
    scrollTo(x: number | {
        x: number;
        y: number;
        animated: boolean;
    }, y: number, animated?: boolean): void;
    /**
     * Scrolls to a given x, y offset, with specific duration of animation.
     *
     * @param {number} x - Scroll to horizon position X.
     * @param {number} y - Scroll To vertical position Y.
     * @param {number} duration - Duration of animation execution time, with ms unit.
     *                            By default is 1000ms.
     */
    scrollToWithDuration(x?: number, y?: number, duration?: number): void;
    /**
     * @ignore
     */
    render(): JSX.Element;
}
export default ScrollView;
