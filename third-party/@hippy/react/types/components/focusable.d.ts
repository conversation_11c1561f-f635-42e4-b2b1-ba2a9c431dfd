import React from 'react';
interface FocusableProps {
    requestFocus?: boolean;
    style?: HippyTypes.Style;
    noFocusStyle?: HippyTypes.Style;
    focusStyle?: HippyTypes.Style;
    nextFocusDownId?: string;
    nextFocusUpId?: string;
    nextFocusLeftId?: string;
    nextFocusRightId?: string;
    onClick?: () => void;
    onFocus?: (evt: HippyTypes.FocusEvent) => void;
}
interface FocusableState {
    isFocus: boolean;
}
/**
 * @noInheritDoc
 */
declare class Focusable extends React.Component<FocusableProps, FocusableState> {
    /**
     * @ignore
     */
    constructor(props: FocusableProps);
    /**
     * @ignore
     */
    render(): JSX.Element;
    private handleFocus;
}
export default Focusable;
