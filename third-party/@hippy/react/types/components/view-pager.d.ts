import React from 'react';
interface PageSelectedEvent {
    position: number;
}
interface PageScrollEvent {
    position: number;
    offset: number;
}
declare type PageScrollState = 'idle' | 'dragging' | 'settling';
interface PageScrollStateEvent {
    pageScrollState: PageScrollState;
}
interface ViewPagerProps {
    /**
     * Specific initial page after rendering.
     *
     * Default: 0
     */
    initialPage: number;
    /**
     * When `false`, the view cannot be scrolled via touch interaction.
     *
     * Default: true
     *
     * > Note that the view can always be scrolled by calling setPage.
     */
    scrollEnabled?: boolean;
    /**
     * Fires at most once per page is selected
     *
     * @param {Object} evt - Page selected event data.
     * @param {number} evt.position - Page index of selected.
     */
    onPageSelected?: (evt: PageSelectedEvent) => void;
    /**
     * Called when the page scroll starts.
     *
     * @param {Object} evt - Page scroll event data.
     * @param {number} evt.position - Page index that will be selected.
     * @param {number} evt.offset - Scroll offset while scrolling.
     */
    onPageScroll?: (evt: PageScrollEvent) => void;
    /**
     * Called when the page scroll state changed.
     *
     * @param {string} evt - Page scroll state event data
     * This can be one of the following values:
     *
     * * idle
     * * dragging
     * * settling
     */
    onPageScrollStateChanged?: (evt: PageScrollState) => void;

    style?: HippyTypes.Style | HippyTypes.Style[];
}
/**
 * Container that allows to flip left and right between child views.
 * Each child view of the ViewPage will be treated as a separate page
 * and will be stretched to fill the ViewPage.
 * @noInheritDoc
 */
declare class ViewPager extends React.Component<ViewPagerProps, {}> {
    private instance;
    constructor(props: ViewPagerProps);
    onPageScrollStateChanged(params: PageScrollStateEvent): void;
    setPage(selectedPage: number | undefined): void;
    setPageWithoutAnimation(selectedPage: number | undefined): void;
    render(): JSX.Element;
}
export default ViewPager;
