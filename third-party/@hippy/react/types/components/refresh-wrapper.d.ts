import React, { ReactElement } from 'react';
interface RefreshWrapperProps {
    bounceTime?: number;
    onRefresh?: () => void;
    getRefresh?: () => ReactElement;
}
/**
 * Simply to implement the drag down to refresh feature.
 *
 * @deprecated
 * @noInheritDoc
 */
declare class RefreshWrapper extends React.Component<RefreshWrapperProps, {}> {
    private instance;
    private refreshComplected;
    constructor(props: RefreshWrapperProps);
    /**
     * Call native for start refresh.
     */
    startRefresh(): void;
    /**
     * Call native that data is refreshed
     */
    refreshCompleted(): void;
    /**
     * @ignore
     */
    render(): JSX.Element;
    private getRefresh;
}
export default RefreshWrapper;
