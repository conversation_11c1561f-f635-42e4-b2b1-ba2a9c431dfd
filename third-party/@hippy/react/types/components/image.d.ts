import React from 'react';
import { LayoutableProps, ClickableProps } from '../types';
import { prefetch } from '../modules/image-loader-module';
interface ImageSource {
    uri: string;
}
interface ImageProps extends LayoutableProps, ClickableProps {
    /**
     * Single image source
     */
    src?: string;
    /**
     * Image source object
     */
    source?: ImageSource | ImageSource[] | null;
    srcs?: string[];
    sources?: ImageSource[];
    /**
     * Image placeholder when image is loading.
     * Support base64 image only.
     */
    defaultSource?: string | undefined;
    /**
     * Fill color to the image
     */
    tintColor?: HippyTypes.tintColor;
    tintColors?: HippyTypes.tintColors;
    /**
     * Image style when `Image` have other children.
     */
    imageStyle?: HippyTypes.Style;
    /**
     * Image ref when `Image` have other children.
     */
    imageRef?: React.ReactNode;
    /**
     * Image resize mode, as same as containMode
     */
    resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
    /**
     * When the image is resized, the corners of the size specified by capInsets
     * will stay a fixed size, but the center content and borders of the image will be stretched.
     * This is useful for creating resizable rounded buttons, shadows, and other resizable assets.
     */
    capInsets?: {
        top: number;
        right: number;
        bottom: number;
        left: number;
    };
    style: HippyTypes.Style;
    /**
     * Invoked on `Image` is loaded.
     */
    onLoad?: () => void;
    /**
     * Invoke on `Image` is end of loading.
     */
    onLoadEnd?: () => void;
    /**
     * Invoke on `Image` is start to loading.
     */
    onLoadStart?: () => void;
    /**
     * Invoke on loading of `Image` get error.
     *
     * @param {Object} evt - Loading error data.
     * @param {string} evt.error - Loading error message.
     */
    onError?: (evt: {
        error: string;
    }) => void;
    /**
     * Invoke on Image is loading.
     *
     * @param {Object} evt - Image loading progress data.
     * @param {number} evt.loaded - The image is loaded.
     * @param {number} evt.total - The loaded progress.
     */
    onProgress?: (evt: {
        loaded: number;
        total: number;
    }) => void;
}
/**
 * A React component for displaying different types of images, including network images,
 * static resources, temporary local images, and images from local disk, such as the camera roll.
 * @noInheritDoc
 */
declare class Image extends React.Component<ImageProps, {}> {
    static get resizeMode(): {
        contain: string;
        cover: string;
        stretch: string;
        center: string;
        repeat: string;
    };
    static prefetch: typeof prefetch;
    static getSize(url: any, success: (width: number, height: number) => void, failure: (err: typeof Error) => void): Promise<unknown>;
    render(): JSX.Element;
    private getImageUrls;
    private handleTintColor;
}
export default Image;
