import React from 'react';
import { LayoutableProps, ClickableProps } from '../types';
interface KeyboardWillShowEvent {
    keyboardHeight: number;
}
interface TextInputProps extends LayoutableProps, ClickableProps {
    /**
     * The value to show for the text input. TextInput is a controlled component,
     * which means the native value will be forced to match this value prop if provided.
     * For most uses, this works great, but in some cases this may cause flickering
     * - one common cause is preventing edits by keeping value the same.
     * In addition to setting the same value, either set editable={false},
     * or set/update maxLength to prevent unwanted edits without flicker.
     */
    value?: string;
    /**
     * Provides an initial value that will change when the user starts typing.
     * Useful for use-cases where you do not want to deal with listening to events
     * and updating the value prop to keep the controlled state in sync.
     */
    defaultValue?: string;
    /**
     * If `false`, text is not editable.
     *
     * Default: true
     */
    editable?: boolean;
    /**
     * Determines which keyboard to open, e.g.`numeric`.
     *
     * The following values work across platforms:
     * * `default`
     * * `number-pad`
     * * `decimal-pad`
     * * `numeric`
     * * `email-address`
     * * `phone-pad`
     * * `search`
     */
    keyboardType?: 'default' | 'numeric' | 'password' | 'email' | 'phone-pad' | 'search';
    /**
     * Determines how the return key should look.
     *
     * The following values work across platforms:
     * * `done`
     * * `go`
     * * `next`
     * * `search`
     * * `send`
     */
    returnKeyType?: 'done' | 'go' | 'next' | 'search' | 'send';
    /**
     * Limits the maximum number of characters that can be entered.
     * Use this instead of implementing the logic in JS to avoid flicker.
     */
    maxLength?: number;
    /**
     * If `true`, the text input can be multiple lines. The default value is `false`.
     * It is important to note that this aligns the text to the top on iOS,
     * and centers it on Android. Use with textAlignVertical set to top for the same behavior
     * in both platforms.
     */
    multiline?: boolean;
    /**
     * Sets the number of lines for a TextInput.
     * Use it with multiline set to true to be able to fill the lines.
     */
    numberOfLines?: number;
    /**
     * If `true`, focuses the input on `componentDidMount`.
     *
     * Default: false
     */
    autoFocus?: boolean;
    /**
     * The color of the `TextInput` underline.
     */
    underlineColorAndroid?: string;
    /**
     * The string that will be rendered before text input has been entered.
     */
    placeholder?: string;
    /**
     * The text color of the placeholder string.
     */
    placeholderTextColor?: string;
    /**
     * The text colors array of the placeholder string.
     */
    placeholderTextColors?: string[];
    style?: HippyTypes.Style;
    /**
     * Callback that is called when the text input is blurred.
     */
    onBlur?: () => void;
    /**
     * Callback that is called when text input ends.
     */
    onEndEditing?: () => void;
    /**
     * Callback that is called when the text input's text changes.
     * Changed text is passed as a single string argument to the callback handler.
     *
     * @param {string} text - Text content.
     */
    onChangeText?: (text: string) => void;
    /**
     * Callback that is called when the text input's content size changes.
     *
     * @param {Object} evt - Content size change event data.
     * @param {number} evt.nativeEvent.contentSize.width - Width of content.
     * @param {number} evt.nativeEvent.contentSize.height - Height of content.
     */
    onContentSizeChange?: (evt: {
        nativeEvent: {
            contentSize: {
                width: number;
                height: number;
            };
        };
    }) => void;
    /**
     * Callback that is called when keyboard popup
     *
     * @param {Object} evt - Keyboard will show event data.
     * @param {number} evt.keyboardHeight - Keyboard height.
     */
    onKeyboardWillShow?: (evt: KeyboardWillShowEvent) => void;
    /**
     * Callback that is called when the text input selection is changed.
     *
     * @param {Object} evt -  Selection change event data.
     * @param {number} evt.nativeEvent.selection.start - Start index of selection
     * @param {number} evt.nativeEvent.selection.end - End index of selection.
     */
    onSelectionChange?: (evt: {
        nativeEvent: {
            selection: {
                start: number;
                end: number;
            };
        };
    }) => void;
}
/**
 * A foundational component for inputting text into the app via a keyboard. Props provide
 * configurability for several features, such as auto-correction, auto-capitalization,
 * placeholder text, and different keyboard types, such as a numeric keypad.
 * @noInheritDoc
 */
declare class TextInput extends React.Component<TextInputProps, {}> {
    private instance;
    private _lastNativeText?;
    constructor(props: TextInputProps);
    componentDidMount(): void;
    /**
     * @ignore
     */
    componentWillUnmount(): void;
    /**
     * Get the content of `TextInput`.
     *
     * @returns {Promise<string>}
     */
    getValue(): Promise<string>;
    /**
     * Set the content of `TextInput`.
     *
     * @param {string} value - New content of TextInput
     * @returns {string}
     */
    setValue(value: string): string;
    /**
     * Make the `TextInput` focused.
     */
    focus(): void;
    /**
     * Make the `TextInput` blurred.
     */
    blur(): void;
    /**
     * Show input method selection dialog.
     * @deprecated
     */
    showInputMethod(): void;
    /**
     * Hide the input method selection dialog.
     * @deprecated
     */
    hideInputMethod(): void;
    /**
     * Clear the content of `TextInput`
     */
    clear(): void;
    /**
     * @ignore
     */
    render(): JSX.Element;
    private onChangeText;
    private onKeyboardWillShow;
}
export default TextInput;
