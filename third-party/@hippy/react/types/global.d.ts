declare const AsyncStorage: HippyTypes.AsyncStorage, Bridge: HippyTypes.Bridge, Device: {
    cancelVibrate: () => void;
    vibrate: (pattern: number, repeatTimes?: number | undefined) => void;
    platform: {
        Localization: {
            country: string;
            language: string;
            direction: number;
        } | undefined;
        OS: HippyTypes.Platform;
        APILevel?: number | undefined;
    };
    screen: HippyTypes.Sizes;
    window: HippyTypes.Sizes;
}, UIManager: {
    createNode: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void;
    deleteNode: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void;
    endBatch: () => void;
    flushBatch: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void;
    sendRenderError: (err: Error) => void;
    startBatch: () => void;
    updateNode: (rootViewId: number, queue: HippyTypes.NativeNode[]) => void;
}, HippyRegister: {
    regist: (appName: string, entryFunc: (...args: any[]) => void) => void;
}, addEventListener: (eventName: string, listener: () => void) => void, removeEventListener: (eventName: string, listener?: (() => void) | undefined) => void, dispatchEvent: (eventName: string, ...args: any[]) => void;
export { addEventListener, removeEventListener, dispatchEvent, AsyncStorage, Bridge, Device, HippyRegister, UIManager, };
