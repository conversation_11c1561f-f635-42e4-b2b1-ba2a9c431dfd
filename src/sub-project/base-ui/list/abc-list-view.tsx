/**
 * create by dengjie
 * desc: 拓展基础组件ListView 带入顶部和底部的显示内容
 * create date 2020/8/3
 */
import React from "react";
import { ListView, ListViewProps, RefreshWrapper, Style, Text, View } from "@hippy/react";
import { BaseComponent } from "../base-component";
import _ from "lodash";
import { LoadingWithTextView } from "../views/loading-view";
import { AnyType } from "../../common-base-module/common-types";
import { ignore } from "../../common-base-module/global";
import { Sizes, TextStyles } from "../../theme";
import { AccessibilityLabelType } from "../../data/constants/accessibility-label-type";
import { AbcView } from "../views/abc-view";

interface AbcListViewProps extends ListViewProps {
    type?: string;
    key?: string;
    itemViewType?: string;
    sticky?: boolean;
    style?: Style;
    onLayout?: (evt: any) => void;

    loading?: boolean;
    finished?: boolean;
    error?: boolean;
    loadingText?: string;
    finishedText?: string;
    errorText?: string;

    preloadItemNumber?: number; //default 20, 预加载条数，当剩余显示数量小于此值时，将会触发onReachEnded事件

    onRefresh?(): void;
}

const kLoadingType = "__loading_"; //用于在底部显示"加载更多..."
const kFinishType = "__finish__"; //用于在底部显示"没有更多数据"
export class AbcListView extends BaseComponent<AbcListViewProps> {
    private _refresh?: RefreshWrapper | null;
    private _listView?: ListView | null;

    static defaultProps = {
        initialListSize: 20,
        preloadItemNumber: 20,
    };
    private showBottomLoading = false;
    private _allowRenderRowCount: number; //当前允许渲染的行数
    private readonly _innerPageSize: number; //内部分页加载，页大小

    /**
     * 由于内部分页，跳转到指定位置时，由于指定元素未加载出来，导致scroll失败, 需要先记录要滚动的位置，当指定项目渲染好的再调用滚动
     */
    private _waitScrollToIndex?: {
        yIndex: number;
        xIndex: number;
        animated: boolean;
    };

    constructor(props: AbcListViewProps) {
        super(props);
        this._innerPageSize = this._allowRenderRowCount = (this.props.initialListSize ?? 20) * 3; //
    }

    componentWillReceiveProps(nextProps: Readonly<AbcListViewProps> /*, nextContext: any*/): void {
        if (!nextProps.loading) {
            // @ts-ignore
            this._refresh?.refreshComplected();
            this.showBottomLoading = true;
        }
    }

    public scrollToIndex(xIndex: number, yIndex: number, animated: boolean): void {
        if (yIndex < this._allowRenderRowCount) {
            this._listView?.scrollToIndex(xIndex, yIndex, animated);
            return;
        }

        this._waitScrollToIndex = { yIndex: yIndex, xIndex: xIndex, animated: animated };
        this._allowRenderRowCount = yIndex + 10; //这里加10只是为了让需要显示的元素尽量出现在列表顶部
        this.setState({});
    }

    private _reformRenderRow(): AnyType[] {
        const { loading, finished, dataSource } = this.props;
        let _dataSource: AnyType[];
        if (this._allowRenderRowCount < dataSource.length) {
            _dataSource = dataSource.slice(0, this._allowRenderRowCount);
        } else {
            _dataSource = [...dataSource];
        }

        if (_dataSource.length) {
            if (loading && this.showBottomLoading) {
                _dataSource.push({
                    _type: kLoadingType,
                });
            } else if (finished) {
                _dataSource.push({
                    _type: kFinishType,
                });
            }
        }

        return _dataSource;
    }

    _renderRow(item: AnyType, dataSource: AnyType[], index?: number): JSX.Element {
        const { renderRow } = this.props;
        const lastItem = _.findLast(dataSource, (item) => item._type === undefined);
        //倒数2个可以认为是最后一个元素，
        if (item._type == kLoadingType) {
            return <LoadingWithTextView />;
        } else if (item._type == kFinishType) {
            return (
                <View style={{ marginHorizontal: Sizes.dp4, marginTop: Sizes.dp16, marginBottom: Sizes.dp2, alignItems: "center" }}>
                    <Text style={TextStyles.t12NT4}>{this.props.finishedText ?? "没有更多内容了"}</Text>
                </View>
            );
        } else {
            return (
                <AbcView
                    airTestKey={`${index}`}
                    onLayout={
                        lastItem === item && this._waitScrollToIndex
                            ? () => {
                                  if (!this._waitScrollToIndex) {
                                      return;
                                  }
                                  const { xIndex, yIndex, animated } = this._waitScrollToIndex;
                                  this._waitScrollToIndex = undefined;
                                  setTimeout(() => this._listView?.scrollToIndex(xIndex, yIndex, animated), 0);
                              }
                            : undefined
                    }
                >
                    {renderRow?.(item)}
                </AbcView>
            );
        }
    }

    render(): JSX.Element {
        const {
            onRefresh,
            loading,
            finished,
            error,
            loadingText,
            finishedText,
            errorText,
            renderRow,
            numberOfRows,
            dataSource,
            onEndReached,
            ...others
        } = this.props;
        ignore(loading, finished, error, finishedText, errorText, renderRow, numberOfRows, dataSource, onEndReached);
        const _dataSource = this._reformRenderRow();
        const listView = (
            <ListView
                accessibilityLabel={AccessibilityLabelType.LISTVIEW}
                ref={(ref) => {
                    this._listView = ref;
                }}
                style={{ flex: 1 }}
                renderRow={(item, ignore, index) => this._renderRow(item, _dataSource, index)}
                dataSource={_dataSource}
                numberOfRows={_.size(_dataSource)}
                onEndReached={this._onEndReached.bind(this)}
                {...others}
                getRowKey={(index) => {
                    if (_dataSource[index]._type != undefined) return _dataSource[index]._type;
                    return this.props.getRowKey?.(index) ?? "";
                }}
            />
        );
        if (!onRefresh) return listView;

        return (
            <RefreshWrapper
                //@ts-ignore
                style={{ flex: 1 }}
                ref={(ref) => {
                    this._refresh = ref;
                }}
                bounceTime={300}
                getRefresh={() => {
                    return <LoadingWithTextView text={loadingText} withAnimation={loading} />;
                }}
                onRefresh={() => {
                    this.showBottomLoading = false;
                    onRefresh?.();
                }}
            >
                {listView}
            </RefreshWrapper>
        );
    }

    private _onEndReached() {
        const { loading, onEndReached, dataSource } = this.props;
        const oldRenderIndex = this._allowRenderRowCount;
        this._allowRenderRowCount += this._innerPageSize; //允许下载下一页
        //当前还处于内部分页显示过程中
        if (oldRenderIndex < dataSource.length) {
            this.setState({});
            return;
        }

        if (loading) return;
        onEndReached?.();
        // @ts-ignore
        this._refresh?.refreshComplected();
    }

    /**
     * 手工告知 RefreshWrapper 开始刷新，展开刷新栏
     */
    public startRefresh(): void {
        this._refresh?.startRefresh();
    }
}
