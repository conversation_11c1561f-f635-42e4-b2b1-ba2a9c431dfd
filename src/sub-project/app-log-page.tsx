/**
 * create by <PERSON><PERSON>
 * desc:
 * create date 2021/7/16
 */
import React from "react";
import { Dimensions, ListView, Text, View } from "@hippy/react";
import { BasePage, ToolBar, ToolBarButtonStyle1 } from "./base-ui";
import { LogUtils } from "./common-base-module/log";
import { ABCStyles, Colors, FontSizes, Sizes, TextStyles } from "./theme";
import abcOverlay, { OverlayViewKey } from "./base-ui/views/abc-overlay";
import { BaseComponent } from "./base-ui/base-component";
import { sharedPreferences } from "./base-business/preferences/shared-preferences";
import { UrlUtils } from "./common-base-module/utils";
import _ from "lodash";
import { environment } from "./base-business/config/environment";
import { NetworkLogItem } from "./data/log-bean";
import { NetworkLogItemView } from "./views/network-log-item-view";
import sizes from "./theme/sizes";
import { AbcTextInput } from "./base-ui/views/abc-text-input";
import { AppSearchBar } from "./base-ui/app-bar";
import { Toast } from "./base-ui/dialog/toast";

const AppLogOverlayViewLocal = "appLogOverlayViewLocal";

interface AppLogOverlayViewProps {}

interface AppLogOverlayViewState {
    position: { top: number; left: number };
}

export class AppLogOverlayView extends BaseComponent<AppLogOverlayViewProps, AppLogOverlayViewState> {
    touchPointPosition: { top: number; right: number } = { top: 0, right: 0 };

    constructor(props: AppLogOverlayViewProps) {
        super(props);
        const _position = sharedPreferences.getObject(AppLogOverlayViewLocal);
        this.state = {
            position: _position
                ? JSON.parse(_position)
                : {
                      top: Sizes.dp30,
                      left: Dimensions.get("window").width - Sizes.dp70,
                  },
        };
    }

    render(): JSX.Element {
        const { position } = this.state;
        return (
            <View
                style={{
                    position: "absolute",
                    top: position.top,
                    left: position.left,
                    height: Sizes.dp40,
                    width: Sizes.dp40,
                    backgroundColor: "#999",
                    borderRadius: Sizes.dp30,
                }}
                onClick={() => {
                    const { ABCNavigator } = require("./base-ui/views/abc-navigator");
                    const { URLProtocols } = require("./url-dispatcher");
                    abcOverlay.hide(OverlayViewKey.debugLogPage);
                    ABCNavigator.navigateToPage(URLProtocols.ABC_DEBUG_LOG).then(() => {
                        AppLogPage.show();
                    });
                }}
                onTouchDown={(e) => {
                    this.touchPointPosition = {
                        top: e.page_y - position.top,
                        right: e.page_x - position.left,
                    };
                }}
                onTouchEnd={(e) => {
                    position.top = e.page_y - this.touchPointPosition.top;
                    position.left = e.page_x - this.touchPointPosition.right;
                    sharedPreferences.setObject(AppLogOverlayViewLocal, JSON.stringify(position));
                    this.setState({ position });
                }}
                onTouchMove={(e) => {
                    position.top = e.page_y - this.touchPointPosition.top;
                    position.left = e.page_x - this.touchPointPosition.right;
                    this.setState({ position });
                }}
                onTouchCancel={() => {
                    sharedPreferences.setObject(AppLogOverlayViewLocal, JSON.stringify(this.state.position));
                }}
            >
                <Text style={[{ textAlign: "center" }, TextStyles.t14NW.copyWith({ lineHeight: Sizes.dp40 })]}>日志</Text>
            </View>
        );
    }
}

interface AppLogPageProps {}

interface AppLogPageState {
    dataList: NetworkLogItem[];
}

export class AppLogPage extends BasePage<AppLogPageProps, AppLogPageState> {
    private list?: NetworkLogItem[];
    private _textInput?: AbcTextInput | null;

    static show(): void {
        try {
            abcOverlay.show(<AppLogOverlayView />, OverlayViewKey.debugLogPage);
        } catch (e) {
            LogUtils.e("init = " + JSON.stringify(e));
        }
    }

    static hide(): void {
        abcOverlay.hide(OverlayViewKey.debugLogPage);
    }

    private networkLogList: Map<string, NetworkLogItem> = new Map<string, NetworkLogItem>();

    getAppBarTitle(): string {
        return "日志输出";
    }

    constructor(props: AppLogPageProps) {
        super(props);
        this._Logs2NetworkLog();
        this.list = [...this.networkLogList.values()].reverse();
        this.state = {
            dataList: _.cloneDeep(this.list),
        };
    }

    handleRSPNetworkText(text: string): void {
        try {
            const list = text.split(", ");
            const path = UrlUtils.httpString(list[0]);

            const networkView: NetworkLogItem = this.networkLogList.get(path) ?? {
                path: "",
                fullPath: "",
                params: {},
                status: true,
                statusNum: "200",
                response: {},
            };

            networkView.status = list?.[1]?.split(" = ")?.[1] == "true";

            networkView.statusNum = list?.[2]?.split(" = ")?.[1] ?? "500";

            networkView.response = JSON.parse(list?.[3]?.split(" = ")?.[1]) ?? {};

            this.networkLogList.set(path, networkView);
        } catch (e) {
            LogUtils.d("AppLogPage.handleRSPNetworkText error => " + JSON.stringify(e));
        }
    }

    handleREQNetworkText(text: string): void {
        try {
            const list = text.split(", ");
            const path = UrlUtils.httpString(list[0]);
            const networkView: NetworkLogItem = this.networkLogList.get(path) ?? {
                path: "",
                fullPath: "",
                params: {},
                status: true,
                statusNum: "200",
                response: {},
            };

            networkView.fullPath = path;

            networkView.path = UrlUtils.getUrlPath(path);

            networkView.params = _.fromPairs([...UrlUtils.getUrlParams(path).entries()]);

            networkView.methods = list?.[1]?.split(" = ")?.[1] ?? "";

            this.networkLogList.set(path, networkView);
        } catch (e) {
            LogUtils.d("AppLogPage.handleREQNetworkText error => " + JSON.stringify(e));
        }
    }

    handleBodyNetworkText(text: string): void {
        try {
            const list = text.split(", ");
            const path = UrlUtils.httpString(list[0]);
            const networkView: NetworkLogItem = this.networkLogList.get(path) ?? {
                path: "",
                fullPath: "",
                params: {},
                status: true,
                statusNum: "200",
                response: {},
            };

            networkView.body =
                list?.[1]?.split(" = ")?.[1] && list?.[1]?.split(" = ")?.[1] != "undefined" ? JSON.parse(list?.[1]?.split(" = ")?.[1]) : {};

            this.networkLogList.set(path, networkView);
        } catch (e) {
            LogUtils.d("AppLogPage.handleBodyNetworkText error => " + JSON.stringify(e));
        }
    }

    private _Logs2NetworkLog(): void {
        this.networkLogList.clear();
        for (const msg of LogUtils.allLogs) {
            if (msg.includes("ABCApiNetwork._doRequest")) {
                if (msg.includes(", headers = ")) {
                    this.handleREQNetworkText(msg);
                } else if (msg.includes(", body = ")) {
                    this.handleBodyNetworkText(msg);
                } else if (msg.includes("ABCApiNetwork._doRequest rsp")) {
                    this.handleRSPNetworkText(msg);
                }
            }
        }
    }

    private _renderRow(date: NetworkLogItem): JSX.Element {
        return <NetworkLogItemView networkInfo={date} />;
    }

    _createInput(): JSX.Element {
        return (
            <View
                style={[
                    ABCStyles.rowAlignCenter,
                    sizes.paddingLTRB(Sizes.dp16, Sizes.dp2, 0, Sizes.dp2),
                    {
                        backgroundColor: Colors.white,
                    },
                ]}
            >
                <AppSearchBar
                    placeholder={"输入接口搜索"}
                    showBackIcon={false}
                    rightPart={this.getRightAppBarIcons()}
                    autoFocus={false}
                    onChangeText={(value) => {
                        this._filterSearchContent(value);
                    }}
                    placeholderColor={Colors.T6}
                    inputStyle={{
                        backgroundColor: Colors.bg1,
                        height: Sizes.dp36,
                        fontSize: FontSizes.size14,
                    }}
                    searchContainerStyle={{ backgroundColor: Colors.bg1 }}
                    leftIconColor={Colors.T6}
                    bottomLine={false}
                    style={{ flex: 1 }}
                />
                <View style={[ABCStyles.rowAlignCenter, { marginRight: Sizes.dp16 }]}>
                    <Text
                        style={[Sizes.dp36, { color: Colors.T3 }]}
                        onClick={() => {
                            const { ABCNavigator } = require("./base-ui/views/abc-navigator");
                            Toast.show(
                                ABCNavigator.getAllRoutes()
                                    .map((e: { routeName: any }) => e.routeName)
                                    .join("\n"),
                                {
                                    duration: 2000,
                                }
                            );
                        }}
                    >
                        页面堆栈
                    </Text>
                </View>
            </View>
        );
    }

    _onCancel(): void {
        this._textInput?.setValue("");
        this.setState({
            dataList: _.cloneDeep(this.list ?? []),
        });
    }

    renderContent(): JSX.Element | undefined {
        const { dataList } = this.state;
        return (
            <View style={{ flex: 1, backgroundColor: Colors.white }}>
                {this._createInput()}
                <ListView
                    style={{ flex: 1 }}
                    numberOfRows={dataList.length}
                    dataSource={dataList}
                    scrollEventThrottle={300}
                    renderRow={(data) => this._renderRow(data)}
                    getRowKey={(data) => dataList?.[data].fullPath ?? data.toString()}
                />
                <ToolBar hideWhenKeyboardShow={true}>
                    <ToolBarButtonStyle1
                        text={"清空日志"}
                        onClick={() => {
                            LogUtils.clearAllLogs();
                            this.list = [];
                            this.setState({ dataList: [] });
                        }}
                    />
                </ToolBar>
            </View>
        );
    }

    private _filterSearchContent(keyword: string): void {
        const hostName = environment.serverHostName;
        const interfaceList = _.cloneDeep(this.list!).map((item) => {
            return {
                ...item,
                mainPath: item.path?.split(hostName)[1]?.replace(/^.*\/v2\/mobile/, ""),
            };
        });
        const filterContent = interfaceList?.filter((t) => t.mainPath?.includes(keyword.toLocaleLowerCase()));
        this.setState({
            dataList: filterContent ?? [],
        });
    }
}
