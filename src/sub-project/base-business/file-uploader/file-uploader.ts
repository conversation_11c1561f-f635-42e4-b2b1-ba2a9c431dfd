import { LogUtils } from "../../common-base-module/log";
import { OssTokenAgent, OssProxyToken, OssProxyRes } from "./oss/oss-token-agent";
import { ossManager } from "./oss/oss-manager";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-04-24
 *
 * @description
 */

interface UploadFileInfo {
    filePath: string;
    url: string;
}

//文件上传
export class FileUploader {
    public static async initOssToken(): Promise<void> {
        const token = await OssTokenAgent.getOssToken();
        await ossManager.init(token);
    }

    public static async initOssProxyToken(option: OssProxyToken): Promise<OssProxyRes> {
        const token = await OssTokenAgent.getOssProxyToken(option);
        await ossManager.initProxy(token);
        return token;
    }

    /**
     * 上传文件
     * @param file 上传的文件
     * @param bucket
     * @param objectKey
     * @param onProgress 上传递进度回调通知
     */
    static async uploadFile(
        file: string,
        bucket: string,
        objectKey: string,
        onProgress?: (currentProgress: number, totalProgress: number) => void
    ): Promise<UploadFileInfo> {
        LogUtils.d(`FileUploader.uploadFile uploadFile  file = ${file}, bucket = ${bucket}, objectKey = ${objectKey}`);
        await FileUploader.initOssToken();
        const uploader = ossManager.upload(bucket, objectKey, file);

        const subscription = uploader.onProgress.subscribe((progress) => {
            onProgress?.(progress.currentSize, progress.totalSize);
        });

        const result = await uploader.onCompleter;

        subscription.unsubscribe();

        if (result.error) throw result.error;

        return {
            filePath: file,
            url: result.url!,
        };
    }
}
