import { callNativeWithPromise, HippyEventEmitter } from "@hippy/react";
import logUtils from "../../common-base-module/log/log-utils";
import { UUIDGen } from "../../common-base-module/utils";

/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2025-06-30 16:23:55
 *
 * @description SSE (Server-Sent Events) 客户端实现
 */

/**
 * SSE 连接状态枚举
 */
export enum SseConnectionState {
    CONNECTING = "connecting",
    CONNECTED = "connected",
    DISCONNECTED = "disconnected",
    ERROR = "error",
}

/**
 * SSE 事件类型
 */
export interface SseEvent {
    /** 事件类型 */
    type: string;
    /** 事件数据 */
    data: string;
    /** 事件 ID */
    id?: string;
    /** 重试间隔（毫秒） */
    retry?: number;
}

/**
 * SSE 连接配置
 */
export interface SseConnectionConfig {
    /** 请求 URL */
    url: string;
    /** HTTP 方法，默认 GET */
    method?: string;
    /** 自定义请求头 */
    headers?: HeadersInit;
    /** 请求体（用于 POST 等方法） */
    body?: string;
    /** 是否自动重连，默认 true */
    autoReconnect?: boolean;
    /** 重连间隔（毫秒），默认 3000 */
    reconnectInterval?: number;
    /** 最大重连次数，默认 5 */
    maxReconnectAttempts?: number;
    /** 连接超时时间（毫秒），默认 30000 */
    connectTimeout?: number;
}

/**
 * SSE 连接状态信息
 */
export interface SseConnectionInfo {
    /** 连接 ID */
    connectionId: string;
    /** 连接 URL */
    url: string;
    /** 是否已连接 */
    isConnected: boolean;
    /** 创建时间 */
    createTime: number;
    /** 连接时间 */
    connectTime: number;
    /** 连接持续时间 */
    connectionDuration: number;
    /** 最后事件 ID */
    lastEventId?: string;
}

/**
 * SSE 事件监听器类型
 */
export interface SseEventListeners {
    /** 连接打开事件 */
    onopen?: (event: { connectionId: string }) => void;
    /** 接收消息事件 */
    onmessage?: (event: SseEvent & { connectionId: string }) => void;
    /** 连接错误事件 */
    onerror?: (event: { connectionId: string; error: string }) => void;
    /** 连接关闭事件 */
    onclose?: (event: { connectionId: string }) => void;
}

/**
 * SSE 连接响应
 */
export interface SseConnectionResponse {
    /** HTTP 状态码 */
    statusCode: number;
    /** HTTP 状态文本 */
    statusLine: string;
    /** 连接 ID */
    connectionId: string;
    /** 响应头 */
    respHeaders: { [key: string]: string[] };
}

/**
 * SSE 客户端类
 * 参考 file-downloader.ts 的封装模式和 AbcNetworkModule.java 的架构设计
 */
export class SseClient {
    private connectionId: string;
    private config: Required<SseConnectionConfig>;
    private listeners: SseEventListeners;
    private hippyEventEmitter: HippyEventEmitter;
    private eventHandlers: any[] = [];
    private currentState: SseConnectionState = SseConnectionState.DISCONNECTED;
    private reconnectAttempts = 0;
    private reconnectTimer?: any;

    /**
     * 构造函数
     * @param config SSE 连接配置
     * @param listeners 事件监听器
     */
    constructor(config: SseConnectionConfig, listeners: SseEventListeners = {}) {
        this.connectionId = UUIDGen.generate();
        this.config = this.normalizeConfig(config);
        this.listeners = listeners;
        this.hippyEventEmitter = new HippyEventEmitter();

        this.setupEventListeners();

        logUtils.d(`SseClient created with connectionId: ${this.connectionId}, url: ${this.config.url}`);
    }

    /**
     * 标准化配置参数
     */
    private normalizeConfig(config: SseConnectionConfig): Required<SseConnectionConfig> {
        return {
            url: config.url,
            method: config.method || "GET",
            headers: config.headers || {},
            body: config.body || "",
            autoReconnect: config.autoReconnect !== false,
            reconnectInterval: config.reconnectInterval || 3000,
            maxReconnectAttempts: config.maxReconnectAttempts || 5,
            connectTimeout: config.connectTimeout || 30000,
        };
    }

    /**
     * 设置事件监听器
     * 参考 file-downloader.ts 的事件处理模式
     */
    private setupEventListeners(): void {
        // 监听 SSE 事件
        const sseEventHandler = this.hippyEventEmitter.addListener("sseEvent", (evt: SseEvent & { connectionId: string }) => {
            if (evt.connectionId === this.connectionId) {
                logUtils.d(`SseClient received event: ${JSON.stringify(evt)}`);
                this.listeners.onmessage?.(evt);
            }
        });

        // 监听 SSE 状态变化
        const sseStatusHandler = this.hippyEventEmitter.addListener(
            "sseStatusChanged",
            (evt: { connectionId: string; status: string; error?: string }) => {
                if (evt.connectionId === this.connectionId) {
                    logUtils.d(`SseClient status changed: ${JSON.stringify(evt)}`);
                    this.handleStatusChange(evt.status as SseConnectionState, evt.error);
                }
            }
        );

        this.eventHandlers.push(sseEventHandler, sseStatusHandler);
    }

    /**
     * 处理连接状态变化
     * 参考 AbcNetworkModule.java 的状态管理模式
     */
    private handleStatusChange(status: SseConnectionState, error?: string): void {
        this.currentState = status;

        switch (status) {
            case SseConnectionState.CONNECTED:
                this.reconnectAttempts = 0;
                this.clearReconnectTimer();
                this.listeners.onopen?.({ connectionId: this.connectionId });
                break;

            case SseConnectionState.ERROR:
                this.listeners.onerror?.({
                    connectionId: this.connectionId,
                    error: error || "Unknown error",
                });
                this.handleReconnection();
                break;

            case SseConnectionState.DISCONNECTED:
                this.listeners.onclose?.({ connectionId: this.connectionId });
                // if (previousState === SseConnectionState.CONNECTED && this.config.autoReconnect) {
                // this.handleReconnection();
                // }
                break;
        }
    }

    /**
     * 处理重连逻辑
     */
    private handleReconnection(): void {
        if (!this.config.autoReconnect || this.reconnectAttempts >= this.config.maxReconnectAttempts) {
            logUtils.w(`SseClient max reconnect attempts reached: ${this.reconnectAttempts}`);
            return;
        }

        this.reconnectAttempts++;
        logUtils.d(`SseClient attempting reconnection ${this.reconnectAttempts}/${this.config.maxReconnectAttempts}`);

        this.clearReconnectTimer();
        this.reconnectTimer = setTimeout(() => {
            this.connect().catch((error) => {
                logUtils.e(`SseClient reconnection failed: ${error}`);
            });
        }, this.config.reconnectInterval);
    }

    /**
     * 清除重连定时器
     */
    private clearReconnectTimer(): void {
        if (this.reconnectTimer) {
            clearTimeout(this.reconnectTimer);
            this.reconnectTimer = undefined;
        }
    }

    /**
     * 连接到 SSE 服务器
     * 参考 file-downloader.ts 的 Promise 模式
     */
    public async connect(): Promise<SseConnectionResponse> {
        if (this.currentState === SseConnectionState.CONNECTING || this.currentState === SseConnectionState.CONNECTED) {
            throw new Error(`SSE connection already in progress or connected. Current state: ${this.currentState}`);
        }

        logUtils.d(`SseClient connecting to: ${this.config.url}`);
        this.currentState = SseConnectionState.CONNECTING;

        try {
            const requestParams = {
                url: this.config.url,
                method: this.config.method,
                connectionId: this.connectionId,
                headers: this.config.headers,
                body: this.config.body,
            };

            const response = await callNativeWithPromise<SseConnectionResponse>("AbcNetwork", "connectSSE", requestParams);

            logUtils.d(`SseClient connected successfully: ${JSON.stringify(response)}`);
            return response;
        } catch (error) {
            this.currentState = SseConnectionState.ERROR;
            logUtils.e(`SseClient connection failed: ${error}`);
            throw error;
        }
    }

    /**
     * 断开 SSE 连接
     * 参考 AbcNetworkModule.java 的断开连接模式
     */
    public async disconnect(): Promise<void> {
        if (this.currentState === SseConnectionState.DISCONNECTED) {
            logUtils.w("SseClient already disconnected");
            return;
        }

        logUtils.d(`SseClient disconnecting: ${this.connectionId}`);

        // 停止自动重连
        this.config.autoReconnect = false;
        this.clearReconnectTimer();

        try {
            await callNativeWithPromise<string>("AbcNetwork", "disconnectSSE", { connectionId: this.connectionId });

            this.currentState = SseConnectionState.DISCONNECTED;
            logUtils.d("SseClient disconnected successfully");
        } catch (error) {
            logUtils.e(`SseClient disconnect failed: ${error}`);
            throw error;
        }
    }

    /**
     * 获取当前连接状态
     */
    public getConnectionState(): SseConnectionState {
        return this.currentState;
    }

    /**
     * 获取连接 ID
     */
    public getConnectionId(): string {
        return this.connectionId;
    }

    /**
     * 获取连接配置
     */
    public getConfig(): Readonly<Required<SseConnectionConfig>> {
        return { ...this.config };
    }

    /**
     * 是否已连接
     */
    public isConnected(): boolean {
        return this.currentState === SseConnectionState.CONNECTED;
    }

    /**
     * 更新事件监听器
     */
    public updateListeners(listeners: Partial<SseEventListeners>): void {
        this.listeners = { ...this.listeners, ...listeners };
        logUtils.d(`SseClient listeners updated for connection: ${this.connectionId}`);
    }

    /**
     * 销毁连接并清理资源
     * 参考 file-downloader.ts 的资源清理模式
     */
    public async destroy(): Promise<void> {
        logUtils.d(`SseClient destroying connection: ${this.connectionId}`);

        // 断开连接
        if (this.currentState !== SseConnectionState.DISCONNECTED) {
            try {
                await this.disconnect();
            } catch (error) {
                logUtils.e(`SseClient destroy disconnect failed: ${error}`);
            }
        }

        // 清理定时器
        this.clearReconnectTimer();

        // 移除事件监听器
        this.eventHandlers.forEach((handler) => {
            handler.remove();
        });
        this.eventHandlers = [];

        // 清理引用
        this.listeners = {};

        logUtils.d(`SseClient destroyed: ${this.connectionId}`);
    }
}

/**
 * SSE 客户端管理器
 * 参考 AbcNetworkModule.java 的连接管理模式
 */
export class SseClientManager {
    private static instance: SseClientManager;
    private connections: Map<string, SseClient> = new Map();

    /**
     * 获取单例实例
     */
    public static getInstance(): SseClientManager {
        if (!SseClientManager.instance) {
            SseClientManager.instance = new SseClientManager();
        }
        return SseClientManager.instance;
    }

    /**
     * 创建 SSE 连接
     */
    public createConnection(config: SseConnectionConfig, listeners: SseEventListeners = {}): SseClient {
        const client = new SseClient(config, listeners);
        this.connections.set(client.getConnectionId(), client);

        logUtils.d(`SseClientManager created connection: ${client.getConnectionId()}`);
        return client;
    }

    /**
     * 获取连接
     */
    public getConnection(connectionId: string): SseClient | undefined {
        return this.connections.get(connectionId);
    }

    /**
     * 移除连接
     */
    public async removeConnection(connectionId: string): Promise<void> {
        const client = this.connections.get(connectionId);
        if (!client) {
            return;
        }

        await client.destroy();
        this.connections.delete(connectionId);

        logUtils.d(`SseClientManager removed connection: ${connectionId}`);
    }

    /**
     * 获取所有连接信息
     */
    public async getAllConnections(): Promise<SseConnectionInfo[]> {
        try {
            const response = await callNativeWithPromise<{
                connections: SseConnectionInfo[];
                totalCount: number;
            }>("AbcNetwork", "getSseConnections", {});

            return response.connections;
        } catch (error) {
            logUtils.e(`SseClientManager get all connections failed: ${error}`);
            return [];
        }
    }

    /**
     * 断开所有连接
     */
    public async disconnectAll(): Promise<void> {
        const promises = Array.from(this.connections.values()).map((client) =>
            client.destroy().catch((error) => logUtils.e(`SseClientManager disconnect failed: ${error}`))
        );

        await Promise.all(promises);
        this.connections.clear();

        logUtils.d("SseClientManager disconnected all connections");
    }

    /**
     * 获取连接数量
     */
    public getConnectionCount(): number {
        return this.connections.size;
    }
}
