/**
 * 成都字节星球科技公司
 * <AUTHOR>
 * @date 2021-03-16
 *
 * @description 入库相关
 */

import { fromJsonToDate, JsonMapper, JsonProperty } from "../../../common-base-module/json-mapper/json-mapper";
import { ExtendData, InventoryExtendDataItem, InventoryPharmacyInfo, SearchParams } from "../../data/inventory-bean";
import _, { isNil } from "lodash";
import { PostGoodsStocksCheckOrdersRep } from "../../inventory-check/data/inventory-check-bean";
import { LogUtils } from "../../../common-base-module/log";
import { GoodsInfo, GoodsSubType, GoodsTypeId, TraceableCodeList, AttachmentItem } from "../../../base-business/data/beans";
import { ABCUtils } from "../../../base-ui/utils/utils";
import { NumberUtils } from "../../../common-base-module/utils";
import { PutInStockOrderDetailReq } from "./inventory-in-agent";
import { InventoryInItem, InventoryInReq, PharmacyListItem } from "../../../data/goods/goods-agent";
import { Clinic, userCenter } from "../../../user-center/user-center";
import { ignore } from "../../../common-base-module/global";

export enum InventoryInType {
    scan = 1, // 扫码添加（入库）
    manual = 2, // 手动添加（入库）
    onlineDraft = 3, // 线上草稿
    photo = 4, // 拍照添加（入库）
}

export enum InventoryInTabType {
    detail = "detail", // 入库单
    create = "create", // 新增入库单
}

export enum InventoryInDetailStatus {
    onlineDraft = -30, //线上草稿
    draft = -20, //本地草稿
    waitAgree = 0, // 待审核
    waitConfirm = 1, //待确认
    pass = 2, // 已入库
    refused = 9, //已拒绝
    revoke = 31, // 已撤回
}

/**
 * 采购入库与退货出库类型
 */
export enum InventoryInAndOutType {
    purchaseWarehousing = 0, //采购入库
    returnGoodsForDelivery = 10, //退货出库
    MALL_IN = 1, //  商城订单入库
    GOODS_IN_SHIP = 2, // // 随货单入库
    JENKINS = 3, // Jenkins导入入库
    ONE_KEY_TAKE_IN_ORDER = 4, // 一键收货入库单
    ONE_KEY_RECEIVE_IN_ORDER = 5, // 一键验收入库单
    CORRECT_IN_ORDER_ADD_MORE = 7, // 修正入库单
    RETURN_OUT = 10, // 退货出库单
    CORRECT_RETURN_OUT = 11, // 修正退货出库单
    RETURN_APPLICATION_OUT = 18, // 退货申请出库单
}

export class InventoryInCreatedUser {
    id?: string;
    name?: string;
}

export class InventoryStockOutListRows {
    batchNo?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date;
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo;
    id?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    orderId?: string;
    packageCostPrice?: number;
    packageCount?: number;
    pieceCount?: number;
    pieceNum?: number;
    productionDate?: string;
    returnLeft?: number;
    returnLeftPackageCount?: number;
    returnLeftPieceCount?: number;
    returnPackageCount?: number;
    returnPieceCount?: number;
    useCount?: number;
    useTotalCostPrice?: number;
    useUnit?: string;
    useUnitCostPrice?: number;
}

/**
 * 入库单列表
 */
export class InventoryInListRows {
    id?: string; // 入库单 ID
    orderNo?: string; // 入库单号
    outOrderNo?: string; // 随货单号
    settlementOrderId?: string;
    supplierId?: string; // 供应商 ID
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date; // 创建入库单 日期
    isConfirm?: number;
    isRecon?: number;
    isScan?: any;
    kindCount?: number; // 品数
    isReview?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    confirmDate?: Date; // 确认日期
    @JsonProperty({ fromJson: fromJsonToDate })
    reviewDate?: Date; // 审核日期
    @JsonProperty({ fromJson: fromJsonToDate })
    inDate?: Date; // 开始日期
    status?: number; // 入库单状态  0 待审核 、 1 待确认 、2 已入库 、9 已拒绝 、 31 已撤回
    statusName?: string; //0 未知初始状态，10 新建待审批, 20 审批被拒绝 30 完成(审批通过) 40撤回
    sum?: number; // 总数
    count?: number; // 数量
    amount?: number; // 含税金额
    amountExcludingTax?: number; // 不含税金额
    toOrganId?: string;
    comment?: InventoryInComment[];
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date; // 上次修改日期
    mallOrderId?: any; // 购物中心订单Id
    toOrgan?: InventoryInToOrgan; // 入库门店信息
    createdUserId?: string; // 入库人 ID
    @JsonProperty({ type: InventoryInCreatedUser })
    createdUser?: InventoryInCreatedUser; // 入库人
    supplierObj?: InventoryInSupplierObj; // 供应商
    settlementOrder?: any;
    supplier?: string; // 供应商名称

    @JsonProperty({ ignore: true })
    fromDraft = false; // 本字段表示结果是否来自草稿（本地）

    __draftId?: string;
    // 0 采购入库 1 商城订单入库 2 随货单入库 3 Jenkins导入入库 4 一键收货入库单 5 一键验货入库单 6 修正入库单 10 退货出库单 11 修正退货出库单
    type?: InventoryInAndOutType;
    get disableAmendmentOrder(): boolean {
        return this.type == InventoryInAndOutType.CORRECT_IN_ORDER_ADD_MORE || this.type == InventoryInAndOutType.CORRECT_RETURN_OUT;
    }
    typeName?: string;
    @JsonProperty({ type: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;

    @JsonProperty({ type: Array, clazz: InventoryStockOutListRows })
    list?: InventoryStockOutListRows[]; // 退货出库列表

    get isOnlineDraft(): boolean {
        return this.status == InventoryInDetailStatus.onlineDraft;
    }
}
export class InventoryInListStat {
    count?: number; // 入库总数量
    amount?: number; // 总含税金额
    inAmount?: number; // 入库总含税金额
    inCount?: number; // 入库总数量
    returnOutInAmount?: number; // 退货出库总含税金额
    returnOutInCount?: number; // 退货出库总数量
}

export class GetInventoryInRsp {
    count?: number; // 入库总条数
    @JsonProperty({ type: Array, clazz: InventoryInListRows })
    rows?: InventoryInListRows[]; // 入库详细列表
    @JsonProperty({ type: InventoryInListStat })
    stat?: InventoryInListStat; // 入库总数量 / 含税金额
    todoCount?: number;

    __fromDraft?: boolean;
}

export class InventoryInListStatus {
    code?: number; // 200
    message?: string; //  "success"
}

export class InventoryInComment {
    @JsonProperty({ fromJson: fromJsonToDate })
    time?: Date; // 备注时间
    content?: string; // 备注
    employeeId?: string; // 员工 ID
}

export class InventoryInToOrgan {
    id?: string; // 入库门店 ID
    parentId?: string;
    nodeType?: number;
    name?: string; // 入库门店名
    shortName?: string;
    shortNameFirst?: string;
    hisType?: number;
    chainId?: string;
    clinicId?: string;
    viewMode?: number;

    get displayName(): string {
        return this.shortName ?? this.name ?? "";
    }
}

export class InventoryInSupplierObj {
    id?: string; // 供应商 ID
    name?: string; // 供应商名称
    status?: number; // 1
}

/**
 * 入库单-药品详细信息列表
 */
export class InventoryInSheetPageDetailsList {
    id?: string;
    orderId?: number;
    goodsId?: string;
    @JsonProperty({ type: GoodsInfo })
    goods?: GoodsInfo; // 药品明细
    pieceNum?: number;
    batchNo?: string; // 生产批号
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date; // 效期
    @JsonProperty({ fromJson: fromJsonToDate })
    productionDate?: Date; // 生产日期
    packageCostPrice?: number;
    packageCount?: number;
    pieceCount?: number; // 计数
    useUnit?: string; // 数量(单位)
    useCount?: number; // 数量
    returnLeftPackageCount?: number; //可退数量
    returnLeftPieceCount?: number; //可退数量
    returnPieceCount?: number;
    returnPackageCount?: number;
    useUnitCostPrice?: number; // 进价 /g
    useTotalCostPrice?: number; // 含税金额
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date;
    batchId?: number; // 批次 ID
    stock?: InventoryInSheetPageDetailsListStock;
    useTotalCostPriceE?: number;
    returnLeft?: number;
    disable?: number;
    v2DisableStatus?: number;
    status?: number;
    @JsonProperty({ type: InventoryExtendDataItem })
    extendData?: InventoryExtendDataItem;
    @JsonProperty({ type: Array, clazz: TraceableCodeList })
    traceableCodeList?: TraceableCodeList[];

    get inStockCountDisplay(): string {
        return `${this.useCount}${this.useUnit}`;
    }

    get inStockCostDisplay(): string {
        return `${ABCUtils.paddingMoney(this.useUnitCostPrice ?? 0)}/${this.useUnit}`;
    }

    /**
     * @desc 统一为package计数
     */
    get countWithPackageUnit(): number {
        let count = 0;
        if (this.useUnit == this.goods?.packageUnit) {
            count = this.useCount ?? 0;
        } else if (this.useUnit == this.goods?.pieceUnit) {
            count = (this.useCount ?? 0) / (this.pieceNum ?? this.goods?.pieceNum ?? 1);
        }
        return count;
    }

    get calCostPriceTotal(): number {
        const count = this.useCount ?? 0,
            useUnitCostPrice = this.useUnitCostPrice ?? 0;
        if (count && useUnitCostPrice) {
            if (NumberUtils.isFloat(useUnitCostPrice)) {
                return Math.round(useUnitCostPrice * 100000 * count) / 100000;
            } else {
                return useUnitCostPrice * count;
            }
        }
        return 0;
    }

    compareKey(): string {
        return `${this.goods?.id}${this.batchId ?? this.batchNo}`;
    }

    // 库存信息（数量/单位）非实际库存
    get currentStockDisplay(): string {
        let stockStr = "";
        if (this.packageCount && this.goods?.packageUnit) {
            stockStr = `${this.packageCount}${this.goods?.packageUnit}`;
        }
        if (this.pieceCount && this.goods?.pieceUnit) {
            stockStr += `${this.pieceCount}${this.goods?.pieceUnit}`;
        }
        return stockStr;
    }

    // 可退数量（数量/单位）
    get returnLeftStockDisplay(): string {
        let stockStr = "";
        if (this.returnLeftPackageCount && this.goods?.packageUnit) {
            stockStr = `${this.returnLeftPackageCount}${this.goods?.packageUnit}`;
        }
        if (this.returnLeftPieceCount && this.goods?.pieceUnit) {
            stockStr += `${this.returnLeftPieceCount}${this.goods?.pieceUnit}`;
        }
        return stockStr.length ? stockStr : `0${this.goods?.packageUnit}`;
    }

    /**
     * 当前药品是否可退
     */
    get canReturn(): boolean {
        return !!this.returnLeftPackageCount || !!this.returnLeftPieceCount;
    }

    countReturnLeft(): void {
        const useUnit = this.goods?.isChineseMedicine ? this.goods.pieceUnit : this.useUnit ?? "";

        if (useUnit == this.goods?.pieceUnit) {
            this.returnLeftPackageCount = 0;
            this.returnLeftPieceCount = this.returnLeft;
            this.returnPieceCount = this.returnLeft;
        } else if (useUnit == this.goods?.packageUnit) {
            this.returnLeftPackageCount = this.returnLeft;
            this.returnLeftPieceCount = 0;
            this.returnPackageCount = this.returnLeft;
        }
    }

    // 售价
    get sellingPrice(): string {
        return `${this.goods?.packagePrice}/${this.useUnit}`;
    }
}

export class InventoryInUpdateItem extends InventoryInItem {
    id?: string;
}

/**
 * 入库单详情
 */

// 日志详情
export class InventoryInSheetPageDetailsLogs {
    id?: number;
    action?: string; // 日志 动作名 （ 撤回 | 创建入库单 | 审核通过 | 审核不通过 ）
    comment?: string; // 日志 备注
    createdDate?: Date; // 日志动作创建时间
    detail?: InventoryInGoodsDetail;
    creator?: InventoryInCreator;
}

class StockInOrder {
    id?: string;
    orderNo?: string; //可读的出库单号
    stockInOrderId?: string;
    stockInOrderNo?: string;
    supplier?: string;
}
class InventoryInReviewUser {
    id?: string;
    name?: string;
}

export class InventoryInSheetPageDetails {
    id?: string; // 入库单 ID
    orderNo?: string; // 入库单号
    outOrderNo?: string; // 随货单号
    settlementOrderId?: string;
    supplierId?: string; // 供应商 ID
    @JsonProperty({ fromJson: fromJsonToDate })
    createdDate?: Date; // 创建入库单 日期
    isConfirm?: number;
    isRecon?: number;
    isScan?: any;
    kindCount?: number; // 品数
    isReview?: number;
    @JsonProperty({ fromJson: fromJsonToDate })
    confirmDate?: Date; // 确认日期
    @JsonProperty({ fromJson: fromJsonToDate })
    reviewDate?: Date; // 审核日期
    @JsonProperty({ fromJson: fromJsonToDate })
    inDate?: Date; // 开始日期
    status?: number; // 入库单状态  0 待审核 、 1 待确认 、2 已入库 、9 已拒绝 、 31 已撤回
    statusName?: string;
    sum?: number; // 总数
    count?: number; // 数量
    amount?: number; // 含税金额
    amountExcludingTax?: number; // 不含税金额
    toOrganId?: string;
    comment?: InventoryInComment[];
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifiedDate?: Date; // 上次修改日期
    mallOrderId?: any; // 购物中心订单Id
    @JsonProperty({ type: InventoryInToOrgan })
    toOrgan?: InventoryInToOrgan; // 入库门店信息
    createdUserId?: string; // 入库人 ID
    createdUser?: InventoryInCreatedUser; // 入库人
    inspectUser?: InventoryInCreatedUser; // 审核人
    supplierObj?: InventoryInSupplierObj; // 供应商
    settlementOrder?: any;
    supplier?: string; // 供应商名称
    @JsonProperty({ type: Array, clazz: InventoryInSheetPageDetailsList })
    list?: InventoryInSheetPageDetailsList[];
    @JsonProperty({ type: Array, clazz: InventoryInSheetPageDetailsLogs })
    logs?: InventoryInSheetPageDetailsLogs[];
    mallOrderInfo?: null; // 未找到相关
    totalPrice?: number; // 售价合计（含税）
    @JsonProperty({ clazz: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;

    @JsonProperty({ type: StockInOrder })
    stockInOrder?: StockInOrder;
    type?: InventoryInAndOutType; //入库单类型
    // 修正单不可编辑，暂时只能去pc端操作
    get disableEditAmendmentOrder(): boolean {
        return this.type == InventoryInAndOutType.CORRECT_IN_ORDER_ADD_MORE || this.type == InventoryInAndOutType.CORRECT_RETURN_OUT;
    }
    typeName?: string;
    @JsonProperty({ type: InventoryInReviewUser })
    reviewUser?: InventoryInReviewUser;
    applyClinicId?: string; //发起入库单的门店，创建入库单是http头的里面的clinicId;总部发起就是总部门店ID;历史数据这个字段为空
    @JsonProperty({ type: ExtendData })
    extendData?: ExtendData; // 采购相关信息
    static PostInventoryInOrdersRep(data: InventoryInSheetPageDetails, searchParams?: SearchParams): PostGoodsStocksCheckOrdersRep {
        const postReq = new PostGoodsStocksCheckOrdersRep();
        postReq.list = [];

        const goodsInfoMap: Map<string, InventoryInSheetPageDetailsList[]> = new Map<string, InventoryInSheetPageDetailsList[]>();
        data.list?.forEach((item) => {
            const goodsId = item.goods?.id ?? item.goodsId ?? "";
            item.goodsId = goodsId;
            // 批次的beforeChange内容为整个药品的stock
            item.returnPackageCount = item.goods?.packagePrice;
            //
            if (goodsInfoMap.has(goodsId)) {
                const array = goodsInfoMap.get(goodsId) ?? [];
                array.push(item);
                goodsInfoMap.set(goodsId, array);
            } else {
                goodsInfoMap.set(goodsId, [item]);
            }
        });
        goodsInfoMap.forEach((item) => {
            const _item = _.cloneDeep(item);
            postReq.list!.push({
                batchs: _item.map((batchItem) => {
                    // 阿莫西林胶囊 12 片*2板/盒
                    // 批号 900707000 效期 2020-09-09
                    // 入库 8000盒 进价 ¥3000.00 总额 ¥2000.00

                    // 需要更改的参数（生产批号 - 生产日期 - 效期 - 入库数量（盒 瓶） - 进价 - 含税金额）
                    delete batchItem.goods; // 药品信息
                    delete batchItem.batchNo; // 生存批号
                    delete batchItem.productionDate; // 生产日期
                    delete batchItem.expiryDate; // 效期
                    delete batchItem.pieceCount; // 入库数量计数（盒|瓶）
                    delete batchItem.useUnitCostPrice; // 进价 3（/盒）
                    delete batchItem.useTotalCostPrice; // 含税金额

                    batchItem.packageCount = batchItem.packageCount ?? 0; // 入库数量
                    batchItem.pieceCount = batchItem.pieceCount ?? 0; //
                    return batchItem;
                }),
            });
        });

        postReq.comment = data.comment?.[0].content ?? "";
        postReq.clearType = "";
        if (searchParams) {
            postReq.isCheckScope = 1;
            postReq.type = searchParams.types?.[0];
            postReq.subType = searchParams.subTypes?.[0];
            postReq.cMSpec = searchParams.cMSpec?.[0];
        }
        return postReq;
    }

    toPutInStockOrderDetailReq(): PutInStockOrderDetailReq {
        return JsonMapper.deserialize(PutInStockOrderDetailReq, {
            id: this.id,
            clinicId: this.toOrgan?.id,
            supplierId: this.supplierId,
            outOrderNo: this.outOrderNo,
            lastModifiedDate: this.lastModifiedDate,
            list: this.list?.map((item) =>
                JsonMapper.deserialize(InventoryInUpdateItem, {
                    ...item,
                    batchId: item.batchId?.toString(),
                })
            ),
        });
    }

    // 是否是入库单
    get isPurchaseWarehousing(): boolean {
        return this.type != InventoryInAndOutType.returnGoodsForDelivery;
    }
}

export class InventoryInSheetPageDetailsListStock {
    id?: string;
    batchId?: string;
}

export class InventoryInGoodsDetail {
    list?: any[];
    fields?: any[];
}

// 入库人信息
export class InventoryInCreator {
    id?: string; // 入库人 ID
    name?: string; // 入库人 姓名
}

export class SupplierListRowsFields {
    supplierId?: string[];
}

export class ExtendInfo {
    restrictGoodsTypeIds?: number[];
}
/**
 * 供应商信息
 */
export class SupplierItem {
    supplierId?: string;
    name?: string;
    fields?: SupplierListRowsFields;
    id?: string;
    status?: number;
    license?: string;
    contact?: string;
    mobile?: string;
    mark?: string;
    chainId?: string;
    createdBy?: string;
    // lastModified?: any;
    // lastModifiedBy?: any;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    pharmacyType?: number;
    @JsonProperty({ clazz: ExtendInfo })
    extendInfo?: ExtendInfo;
}

export class GetSupplierRsp {
    keyword?: string;
    clinicId?: string;
    offset?: string;
    limit?: number;
    status?: string;
    @JsonProperty({ type: Array, clazz: SupplierItem })
    rows?: SupplierItem[];
    total?: number;
}

/**
 * 入库草稿
 */

export class InventoryInDraftRow {
    supplierId?: string;
    name?: string;
    fields?: InventoryInDraftRowFields;
    id?: string;
    status?: number;
    license?: string;
    contact?: string;
    mobile?: string;
    mark?: string;
    chainId?: string;
    createdBy?: string;
    lastModified?: any;
    lastModifiedBy?: any;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
}

export class InventoryInDraft {
    @JsonProperty({ type: Array, clazz: InventoryInSheetPageDetailsList })
    medicines: InventoryInSheetPageDetailsList[] = [];

    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifyTime = new Date(); // 最近更改时间

    @JsonProperty({ type: InventoryInDraftRow })
    supplier?: InventoryInDraftRow; // 供应商

    clinicId?: string;

    comment?: string; // 备注
    outOrderNo?: string; // 随货单号

    @JsonProperty({ ignore: true })
    hasChanged = false;

    @JsonProperty({ ignore: true })
    hasSubmit = false;

    toInventoryReq(): InventoryInSheetPageDetails {
        const req = new InventoryInSheetPageDetails();
        const supplier = new GetSupplierRsp();
        const items: InventoryInSheetPageDetailsList[] = [];
        this.medicines.forEach((item) => {
            const newItem = new InventoryInSheetPageDetailsList();
            newItem.goodsId = item.goodsId;
            newItem.useUnit = item.useUnit;
            newItem.useCount = item.useCount;
            newItem.useUnitCostPrice = item.useUnitCostPrice;
            newItem.useTotalCostPrice = item.useUnitCostPrice! * item.useCount!;
            newItem.batchNo = item.batchNo;
            newItem.expiryDate = item.expiryDate;
            newItem.productionDate = item.productionDate;

            items.push(newItem);
        });

        req.list = items;
        LogUtils.d("req.list=" + JSON.stringify(req));
        req.supplierId = this.supplier?.supplierId;
        supplier.clinicId = this.clinicId;
        req.comment?.map((comment) => {
            comment.content = this.comment;
        });
        req.outOrderNo = this.outOrderNo;

        return req;
    }
}

export class InventoryInDraftRowFields {
    @JsonProperty()
    supplierId?: string[];
}

/**
 * 搜索药品首字母
 */
export class InventoryInSearchGoods {
    id?: string;
    goodsId?: string;
    shortId?: string;
    organId?: string;
    name?: string;
    py?: string;
    medicineCadn?: string;
    disable?: number;
    type?: number;
    subType?: number;
    dismounting?: number;
    manufacturer?: string;
    isSell?: number;
    packagePrice?: number;
    packageCostPrice?: any;
    piecePrice?: number;
    pieceNum?: number;
    pieceUnit?: string;
    packageUnit?: string;
    medicineDosageNum?: any;
    medicineDosageUnit?: string;
    medicineNmpn?: string;
    materialSpec?: string;
    cMSpec?: string;
    extendSpec?: any;
    position?: string;
    remark?: string;
    smartDispense?: any;
    barCode?: string;
    status?: number;
    inTaxRat?: number;
    outTaxRat?: number;
    chainPiecePrice?: number;
    chainPackagePrice?: number;
    chainPackageCostPrice?: any;
    needExecutive?: number;
    standardName?: any;
    standardUnit?: any;
    typeId?: number;
    customTypeId?: any;
    customTypeName?: any;
    combineType?: number;
    sellConfig?: number;
    inorderConfig?: number;
}

class TraceableCodeTraceableCode {
    idx?: number;
    no?: string;
    used?: number;
}

/**
 * 入库单新增项目详情
 */

export class SpecificationDict {
    pieceNum?: number; // 每件数量
    pieceUnit?: string; // 每件单位
    componentNum?: number; // 套餐数量
    componentUnit?: string; // 套餐单位
    dosageNum?: number; // 剂量数量
    dosageUnit?: string; // 剂量单位
    packageUnit?: string; // 包装单位
}
export class RowMatchInfo {
    medicineCadn?: string;
    specificationDict?: SpecificationDict;
    specification?: string;
    manufacturer?: string;
    manufacturerFull?: string;
    medicineNmpn?: string;
    barCode?: string;
}
export class InventoryInDraftDetailItem extends InventoryInSheetPageDetailsList {
    batchNo?: string; //生产批号

    chainId?: string; //连锁id

    clinicId?: string; //诊所id

    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: Date; //效期

    goodsId?: string; //商品id

    id?: string; //主键id

    orderDraftId?: string; //入库草稿单id

    @JsonProperty({ fromJson: fromJsonToDate })
    productionDate?: Date; //生产日期

    useCount?: number; //数量

    useTotalCostPrice?: number; //成本总价

    useUnit?: string; //单位

    useUnitCostPrice?: number; //成本单价
    @JsonProperty({ type: Array, clazz: TraceableCodeTraceableCode })
    traceableCodeList?: TraceableCodeTraceableCode[];

    rowMatchInfo?: RowMatchInfo; // 行匹配信息（未匹配的药品需要传入这里）
    externalRelatedKey?: string; // 外部关联key
}

export class InventoryMedicineItem {
    @JsonProperty({ type: GoodsInfo })
    goodsInfo?: GoodsInfo;
    qrcode?: string; //条形码

    dosageNum?: number; //剂量
    dosageUnit?: string; //剂量单位
    pieceNum?: number; //制剂数量
    pieceUnit?: string; //制剂单位
    useUnit?: string; //包装单位
    useCount?: number; //入库量

    useUnitCostPrice?: number; //进价

    @JsonProperty({ fromJson: fromJsonToDate })
    expiredTime?: Date; //效期

    @JsonProperty({ fromJson: fromJsonToDate })
    productionDate?: Date; //生产日期

    addInventory?: number; // 入库数量
    batchNumber?: string; //批号

    westernMedicine?: boolean; //药品类型，是否是西药

    dismounting?: boolean; // 是否拆零

    //草稿新增字段
    id?: string;
    chainId?: string;
    clinicId?: string;
    orderDraftId?: string;
    @JsonProperty({ type: Array, clazz: TraceableCodeTraceableCode })
    traceableCodeList?: TraceableCodeTraceableCode[];
    @JsonProperty({ type: InventoryExtendDataItem })
    extendData?: InventoryExtendDataItem;
    rowMatchInfo?: RowMatchInfo;
    externalRelatedKey?: string; // 外部关联key
}

export class InventoryInDraftReq {
    id?: string; // 主键id
    chainId?: string; //连锁id
    clinicId?: string; //诊所id
    comment?: string; //备注

    amount?: number; // 含税金额
    count?: number; // ?? 入库单上 useCount转成大包装单位后的累加
    outOrderNo?: string;

    createBy?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    createdUser?: {
        id?: string;
        name?: string;
    };
    kindCount?: number; // 种类
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date;
    sum?: number;

    @JsonProperty({ type: Array, clazz: InventoryInDraftDetailItem })
    list: InventoryInDraftDetailItem[] = [];

    pharmacyNo?: number; //药房号
    pharmacyType?: number; //药房类型
    pharmacy?: PharmacyListItem;

    supplierId?: string; //供应商id
    supplier?: string; //供应商id

    toOrganId?: string;
    @JsonProperty({ type: InventoryInToOrgan })
    toOrgan?: InventoryInToOrgan; // 入库门店信息
    forceSubmit?: number;
    @JsonProperty({ type: ExtendData })
    extendData?: ExtendData; // 采购相关信息
    inspectBy?: string; // 验收人id

    draftId?: string;
}

export class InventoryDraft {
    status?: number;
    @JsonProperty({ type: Array, clazz: InventoryMedicineItem })
    medicines: InventoryMedicineItem[] = [];

    @JsonProperty({ fromJson: fromJsonToDate })
    lastModifyTime = new Date(); //最近更改时间

    @JsonProperty({ type: SupplierItem })
    supplier?: SupplierItem; //供应商
    inspectUser?: InventoryInCreatedUser; // 审核人

    clinicId?: string;

    comment?: string; //备注
    outOrderNo?: string; //随货单号

    @JsonProperty({ ignore: true })
    hasChanged = false;

    @JsonProperty({ ignore: true })
    hasSubmit = false;

    __draftId?: string;

    @JsonProperty({ type: InventoryInToOrgan })
    toOrgan?: InventoryInToOrgan; // 入库门店信息
    @JsonProperty({ type: Clinic })
    clinic?: Clinic; // 入库门店信息

    @JsonProperty({ clazz: InventoryPharmacyInfo })
    pharmacy?: InventoryPharmacyInfo;
    @JsonProperty({ clazz: ExtendData })
    extendData?: ExtendData; // 采购相关信息

    get totalCost(): number {
        let totalCost = 0.0;
        this.medicines.forEach((item) => {
            if (item.useCount == null || item.useUnitCostPrice == null) {
            }
            totalCost += (item.useCount ?? 0.0) * (item.useUnitCostPrice ?? 0.0);
        });
        return totalCost;
    }

    toInventoryReq(isOnlineDraft?: boolean): InventoryInReq {
        const req = new InventoryInReq();
        const items: InventoryInItem[] = [];
        this.medicines.forEach((item) => {
            const newItem = new InventoryInItem();
            newItem.goodsId = item.goodsInfo?.id;
            newItem.useUnit = item.useUnit;
            newItem.useCount = item.useCount;
            newItem.useUnitCostPrice = item.useUnitCostPrice;
            newItem.useTotalCostPrice = item.useUnitCostPrice! * item.useCount!;
            newItem.batchNo = item.batchNumber;
            newItem.expiryDate = item.expiredTime;
            newItem.productionDate = item.productionDate;
            newItem.traceableCodeList = item?.traceableCodeList;
            newItem.extendData = item?.extendData;

            items.push(newItem);
        });

        req.list = items;
        req.supplierId = this.supplier?.supplierId;
        req.clinicId = this.clinicId;
        req.comment = this.comment;
        req.outOrderNo = this.outOrderNo;
        req.inspectBy = this.inspectUser?.id;
        req.extendData = this.extendData;
        if (!!isOnlineDraft) {
            req.inOrderDraftId = this.__draftId;
        }

        return req;
    }

    toInventoryOnlineDraftReq(pharmacyInfo?: PharmacyListItem): InventoryInDraftReq {
        const req = new InventoryInDraftReq();
        const items: InventoryInDraftDetailItem[] = [];
        this.medicines.forEach((item) => {
            const newItem = new InventoryInDraftDetailItem();
            const _isNeedMatched = (!isNil(item.goodsInfo?._isMatched) && !item.goodsInfo?._isMatched) || !item.goodsInfo?.goodsId;
            // 匹配失败
            if (_isNeedMatched) {
                newItem.goodsId = ""; // 匹配失败时，商品id为空
                newItem.goods = undefined; // 匹配失败时，商品为空
                newItem.rowMatchInfo = item.rowMatchInfo; // 匹配失败时，保存匹配失败的行信息

                newItem.useUnit = item.useUnit;
                newItem.useCount = item.useCount;
                newItem.useUnitCostPrice = item.useUnitCostPrice;
                newItem.useTotalCostPrice = item.useUnitCostPrice! * item.useCount!;
                newItem.batchNo = item.batchNumber;
                newItem.expiryDate = item.expiredTime;
                newItem.productionDate = item.productionDate;

                //草稿新增字段
                newItem.id = item.id;
                newItem.chainId = item.chainId;
                newItem.clinicId = item.clinicId;
                newItem.orderDraftId = item.orderDraftId;
                newItem.traceableCodeList = item?.traceableCodeList;
                newItem.extendData = item?.extendData;
                items.push(newItem);
            } else {
                // 匹配成功
                newItem.goodsId = item.goodsInfo?.id;
                newItem.useUnit = item.useUnit;
                newItem.useCount = item.useCount;
                newItem.useUnitCostPrice = item.useUnitCostPrice;
                newItem.useTotalCostPrice = item.useUnitCostPrice! * item.useCount!;
                newItem.batchNo = item.batchNumber;
                newItem.expiryDate = item.expiredTime;
                newItem.productionDate = item.productionDate;
                newItem.goods = item.goodsInfo;

                //草稿新增字段
                newItem.id = item.id;
                newItem.chainId = item.chainId;
                newItem.clinicId = item.clinicId;
                newItem.orderDraftId = item.orderDraftId;
                newItem.traceableCodeList = item?.traceableCodeList;
                newItem.extendData = item?.extendData;
                items.push(newItem);
            }
        });

        req.list = items;
        req.supplierId = this.supplier?.supplierId;
        req.clinicId = this.clinicId;
        req.comment = this.comment;
        req.outOrderNo = this.outOrderNo;
        req.toOrganId = this.toOrgan?.id ?? "";

        req.pharmacy = pharmacyInfo;
        req.pharmacyNo = pharmacyInfo?.no;
        req.pharmacyType = pharmacyInfo?.type;

        let totalCost = 0;
        let sum = 0;
        const medicineCount: Set<string | undefined> = new Set();
        this.medicines.forEach((item) => {
            if (item.useCount == null || item.useUnitCostPrice == null) {
            }
            sum += item.useCount ?? 0;
            totalCost += (item.useCount ?? 0.0) * (item.useUnitCostPrice ?? 0.0);
            medicineCount.add(item.goodsInfo?.compareKey());
        });

        req.amount = totalCost;
        req.count = sum;
        req.kindCount = medicineCount.size;
        req.sum = sum;
        req.createdUser = {
            id: userCenter.employee?.id ?? userCenter.employee?.employeeId ?? "",
            name: userCenter.employee?.name ?? "",
        };
        req.extendData = this.extendData;
        req.inspectBy = this.inspectUser?.id;

        return req;
    }

    toInventoryInListRows(): InventoryInListRows {
        return JsonMapper.deserialize(InventoryInListRows, {
            status: this.status ?? InventoryInDetailStatus.onlineDraft,
            __draftId: this.__draftId,
            amount: this.totalCost,
            lastModifiedDate: this.lastModifyTime,
            createdUser: { id: userCenter.employee?.id, name: userCenter.employee?.name },
            supplier: this.supplier?.name,
            toOrgan: this.toOrgan,
            pharmacy: this.pharmacy,
        });
    }
}

/**
 * 入库单草稿详情
 */
export class InventoryInDraftDetail {
    id?: string; // 主键id
    chainId?: string; //连锁id
    clinicId?: string; //诊所id
    comment?: string; //备注

    amount?: number; // 含税金额
    count?: number; // ??
    outOrderNo?: string;

    createBy?: string;
    @JsonProperty({ fromJson: fromJsonToDate })
    created?: Date;
    createdUser?: {
        id?: string;
        name?: string;
    };
    kindCount?: number; // 种类
    @JsonProperty({ fromJson: fromJsonToDate })
    lastModified?: Date;

    @JsonProperty({ type: Array, clazz: InventoryInDraftDetailItem })
    list: InventoryInDraftDetailItem[] = [];

    pharmacyNo?: number; //药房号
    pharmacyType?: number; //药房类型
    pharmacy?: PharmacyListItem;

    supplierId?: string; //供应商id
    supplier?: string; //供应商id

    toOrganId?: string;
    @JsonProperty({ type: InventoryInToOrgan })
    toOrgan?: InventoryInToOrgan; // 入库门店信息

    _localDraftDetail?: InventoryDraft;
    @JsonProperty({ type: ExtendData })
    extendData?: ExtendData; // 采购相关信息
    @JsonProperty({ type: InventoryInCreatedUser })
    inspectUser?: InventoryInCreatedUser; // 审核人

    get localDraftDetail(): InventoryDraft {
        if (!this._localDraftDetail) {
            this.toTranLocalDraft();
        }
        //@ts-ignore
        return this._localDraftDetail;
    }

    set localDraftDetail(data: InventoryDraft) {
        this._localDraftDetail = data;
    }

    toInventoryInListRows(): InventoryInListRows {
        const result = this.localDraftDetail.toInventoryInListRows();
        result.id = this.id;
        return result;
    }

    /**
     * 云端草稿转换成本地草稿
     */
    toTranLocalDraft(): void {
        this.localDraftDetail = JsonMapper.deserialize(InventoryDraft, {
            status: InventoryInDetailStatus.onlineDraft,
            medicines: this.list.map((item) => {
                ignore(item.goods?.scrollKey);
                return JsonMapper.deserialize(InventoryMedicineItem, {
                    ...item,
                    goodsInfo: item.goods,
                    expiredTime: item.expiryDate,
                    batchNumber: item.batchNo,
                });
            }),
            lastModifyTime: this.lastModified ?? new Date(),
            supplier: {
                name: this.supplier,
                supplierId: this.supplierId,
            },
            clinicId: this.clinicId,
            comment: this.comment,
            outOrderNo: this.outOrderNo,
            toOrgan: this.toOrgan,
            __draftId: this.id,
            extendData: this.extendData,
            inspectUser: this.inspectUser,
        });
    }
}

// 新建药品-分类列表
export const DrugClassifyList = [
    {
        name: "西药",
        subType: GoodsSubType.medicineWestern,
        typeId: GoodsTypeId.medicineWest,
    },
    {
        name: "中成药",
        subType: GoodsSubType.medicineChinesePatent,
        typeId: GoodsTypeId.medicineChinesePatent,
    },
    {
        name: userCenter.clinic?.isDrugstoreButler ? "配方饮片" : "中药饮片",
        subType: GoodsSubType.medicineChinese,
        typeId: GoodsTypeId.medicineChinesePiece,
    },
    {
        name: "中药颗粒",
        subType: GoodsSubType.medicineChinese,
        typeId: GoodsTypeId.medicineChineseGranule,
    },
];
//新建物资-分类列表
export const MaterialClassifyList = [
    {
        name: "医疗器械",
        subType: GoodsSubType.materialMedical,
        typeId: GoodsTypeId.materialMedical,
    },
    {
        name: "消毒用品",
        subType: GoodsSubType.materialDisinfectant,
        typeId: GoodsTypeId.materialDisinfectant,
        onlyDrugstoreButler: true,
    },
    {
        name: "后勤材料",
        subType: GoodsSubType.materialLogistics,
        typeId: GoodsTypeId.materialLogistics,
    },
    {
        name: "固定资产",
        subType: GoodsSubType.materialFixedAssets,
        typeId: GoodsTypeId.materialFixedAssets,
    },
];
//新建商品-分类列表
export const GoodsClassifyList = [
    {
        name: "自制成品",
        subType: GoodsSubType.goodsHomemade,
        typeId: GoodsTypeId.goodsHomemade,
    },
    {
        name: "保健药品",
        subType: GoodsSubType.healthMedicine,
        typeId: GoodsTypeId.healthMedicine,
    },
    {
        name: "保健食品",
        subType: GoodsSubType.healthGood,
        typeId: GoodsTypeId.healthGood,
    },
    {
        name: "化妆品",
        subType: GoodsSubType.cosmetics,
        typeId: GoodsTypeId.cosmetics,
        onlyDrugstoreButler: true,
    },
    {
        name: "其他商品",
        subType: GoodsSubType.otherGood,
        typeId: GoodsTypeId.otherGood,
    },
];

export class EntityResolutionImageUrls {
    @JsonProperty({ type: Array, clazz: String })
    imageUrls?: string[];
}

/**
 * 入库单实体解析
 */
export class EntityResolutionItem {
    curRow?: number;
    totalRow?: number;
    goodsName?: string;
    goodsSpec?: string;
    goodsManu?: string;
}
export class StockInOrderEntityResolutionRsp {
    @JsonProperty({ type: Array, clazz: EntityResolutionItem })
    entity?: EntityResolutionItem[];
}

/**
 * 获取入库草稿单对应的文件数据响应
 */

export class FileDataRowInvInfoList {
    goodsId?: string; // 商品id
    goods?: GoodsInfo; // 商品信息
    medicineCadn?: string; // 药品名称
    useUnit?: string; // 单位
    useCount?: string; // 数量
}
export class FileDataRowSpecificationDict {
    pieceNum?: number; // 每件数量
    pieceUnit?: string; // 每件单位
    componentNum?: number; // 套餐数量
    componentUnit?: string; // 套餐单位
    dosageNum?: number; // 剂量数量
    dosageUnit?: string; // 剂量单位
    packageUnit?: string; // 包装单位
}
export class StockInOrderFileDataRow {
    id?: string; // 关联id
    @JsonProperty({ type: Array, clazz: FileDataRowInvInfoList })
    invInfoList?: FileDataRowInvInfoList[]; // 部分匹配的商品信息集合
    batchNo?: string; // 生产批号
    isMatched?: number; // 是否匹配 0 不匹配 1 匹配
    @JsonProperty({ fromJson: fromJsonToDate })
    productionDate?: string; // 生产日期
    @JsonProperty({ fromJson: fromJsonToDate })
    expiryDate?: string; // 有效期
    specificationDict?: FileDataRowSpecificationDict; // 规格
    specification?: string; // 规格
    taskId?: string; // 导入草稿单id
    manufacturer?: string; // 生产厂家
    useCount?: string; // 数量
    rawIndex?: string; // 行号
    barCode?: string; // 条码
    medicineCadn?: string; // 药品名称
    useTotalCostPrice?: string; // 总成本
    useUnit?: string; // 单位
    medicineNmpn?: string; // 批准文号
    useUnitCostPrice?: string; // 进价
    mallSpuGoodsId?: number; // 商城spu商品id
    mallSkuGoodsId?: number; // 商城sku商品id
    packageType?: string; // 包装类型
    categoryId?: string; // 商品分类id
    equivalent?: string; // 等效
    externalRelatedKey?: string; // 外部关联key
    orderDraftId?: string; // 入库草稿单id
}
/**
 * 入库单文件数据响应
 */
export class AbcServiceResponseStockInOrderFileData {
    @JsonProperty({ type: Array, clazz: StockInOrderFileDataRow })
    rows?: StockInOrderFileDataRow[]; // 数据行列表
    total?: number; // 总记录数
    offset?: number; // 偏移量
    limit?: number; // 每页条数
    keyword?: string; // 搜索关键词
    supplierId?: string; // 供应商ID
    outOrderNo?: string; // 外部订单号
}
