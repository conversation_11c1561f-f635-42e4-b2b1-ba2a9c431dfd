/**
 * create by <PERSON><PERSON>
 * desc: 请求单详情页面
 * create date 2021/4/14
 */
import React from "react";
import { Text, View } from "@hippy/react";
import { BaseBlocNetworkPage } from "../../base-ui/base-page";
import { InventoryInDetailInvoicePageBloc } from "./inventory-in-detail-invoice-page-bloc";
import { BlocHelper } from "../../bloc/bloc-helper";
import { ListSettingEditItem, ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import _ from "lodash";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import {
    InventoryInAndOutType,
    InventoryInDetailStatus,
    InventoryInSheetPageDetailsList,
    InventoryInType,
} from "./inventory-in-data/inventory-in-bean";
import { userCenter } from "../../user-center";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ar<PERSON>uttonStyle1, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>onStyle2 } from "../../base-ui";
import { AbcListView } from "../../base-ui/list/abc-list-view";
import { BaseComponent } from "../../base-ui/base-component";
import { TimeUtils } from "../../common-base-module/utils";
import { ABCUtils } from "../../base-ui/utils/utils";
import { AbcView } from "../../base-ui/views/abc-view";
import { BlocBuilder } from "../../bloc";
import { ImageUpdateView } from "../../base-ui/image-update-view";

interface InventoryInDetailInvoicePageProps {
    orderId?: string;
}

export class InventoryInDetailInvoicePage extends BaseBlocNetworkPage<InventoryInDetailInvoicePageProps, InventoryInDetailInvoicePageBloc> {
    constructor(props: InventoryInDetailInvoicePageProps) {
        super(props);
        this.bloc = new InventoryInDetailInvoicePageBloc({ orderId: props.orderId });
    }

    getAppBarTitle(): string {
        const state = this.bloc.currentState;
        return `${state.detail?.type != InventoryInAndOutType.returnGoodsForDelivery ? "入" : "出"}库单`;
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        if (!state) return [];
        const bottoms: JSX.Element[] = [];
        if (state.canAddMoreMedicine) {
            if (state.needRevoke && !state.isRevokeEdit) {
            } else {
                bottoms.push(
                    <AbcView key={1} onClick={() => this.bloc.requestSubmit()}>
                        <Text style={TextStyles.t16NM}>完成</Text>
                    </AbcView>
                );
            }
        }
        return bottoms;
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    componentDidMount(): void {
        super.componentDidMount();
        BlocHelper.connectLoadingStatus(this.bloc, this);
    }

    private _renderRefuseView(): JSX.Element {
        const state = this.bloc.currentState,
            isRefuse = state.detail?.status == InventoryInDetailStatus.refused;
        if (!isRefuse) return <View />;
        const refuseDetail = state.detail?.logs?.[0];
        if (!refuseDetail) return <View />;
        return (
            <View
                style={[ABCStyles.rowAlignCenter, Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp8), { backgroundColor: Colors.R4 }]}
            >
                <View style={[ABCStyles.rowAlignCenter, { flex: 1 }]}>
                    <Text style={[TextStyles.t12NR2]}>
                        {`${refuseDetail?.creator?.name} ${refuseDetail.action} ${
                            refuseDetail.comment?.length ? `原因:${refuseDetail.comment}` : ""
                        }`}
                    </Text>
                </View>
            </View>
        );
    }

    private _renderInvoiceBaseInfoView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail,
            isOpenMultiplePharmacy = state.pharmacyInfoConfig?.isOpenMultiplePharmacy;
        const comment = _.last(detail?.comment);
        //是否为入库单
        const isPurchaseWarehousing = detail?.isPurchaseWarehousing;
        // 当前机构所属地区是否是南京
        const isNanjing = userCenter.clinic?.isNanjingClinic;
        return (
            <View style={[{ backgroundColor: Colors.white, paddingLeft: Sizes.listHorizontalMargin }]}>
                <ListSettingItem
                    title={`${isPurchaseWarehousing ? "入" : "出"}库人`}
                    style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                    content={detail?.createdUser?.name ?? "--"}
                    bottomLine={true}
                />
                <ListSettingItem
                    itemStyle={
                        state.updateWithNeedRevoke && state.canUpdateClinic ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal
                    }
                    title={`${isPurchaseWarehousing ? "入" : "出"}库门店`}
                    style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                    content={detail?.toOrgan?.displayName}
                    bottomLine={true}
                    onClick={() => {
                        if (state.updateWithNeedRevoke && state.canUpdateClinic) this.bloc.requestModifyClinic();
                    }}
                />
                {isOpenMultiplePharmacy && (
                    <ListSettingItem
                        itemStyle={
                            (state.updateWithNeedRevoke && state.canUpdateClinic) ||
                            (detail?.status == InventoryInDetailStatus.waitConfirm && !userCenter.clinic?.isChainAdminClinic)
                                ? ListSettingItemStyle.expandIcon
                                : ListSettingItemStyle.normal
                        }
                        title={`${isPurchaseWarehousing ? "入" : "出"}库库房`}
                        style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                        content={detail?.pharmacy?.name}
                        contentHint={!detail?.pharmacy?.name ? `请选择${isPurchaseWarehousing ? "入" : "出"}库库房` : ""}
                        bottomLine={true}
                        onClick={() =>
                            (state.updateWithNeedRevoke && state.canUpdateClinic) ||
                            (detail?.status == InventoryInDetailStatus.waitConfirm &&
                                !userCenter.clinic?.isChainAdminClinic &&
                                this.bloc.requestSelectMultiplePharmacy(detail?.pharmacy?.no))
                        }
                    />
                )}
                {isPurchaseWarehousing && (
                    <ListSettingItem
                        itemStyle={
                            state.canUpdate || (state.updateWithNeedRevoke && state.canAddMoreMedicine)
                                ? ListSettingItemStyle.expandIcon
                                : ListSettingItemStyle.normal
                        }
                        title={"供应商"}
                        style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                        content={detail?.supplier}
                        bottomLine={true}
                        onClick={() => {
                            if (state.canUpdate || (state.updateWithNeedRevoke && state.canAddMoreMedicine))
                                this.bloc.requestModifySupplier();
                        }}
                    />
                )}
                {isPurchaseWarehousing && (
                    <View>
                        {state.canAddMoreMedicine ? (
                            <ListSettingEditItem
                                itemStyle={state.updateWithNeedRevoke ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                                editable={state.updateWithNeedRevoke}
                                title={"随货单号"}
                                style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                                content={detail?.outOrderNo}
                                contentHint={state.updateWithNeedRevoke ? "请填写" : ""}
                                bottomLine={true}
                                onChanged={(text) => {
                                    this.bloc.requestLocalModifyOutOrderNo(text);
                                }}
                            />
                        ) : (
                            <ListSettingItem
                                itemStyle={state.canUpdate ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                                title={"随货单号"}
                                style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                                content={detail?.outOrderNo}
                                contentHint={state.canUpdate ? "请填写" : ""}
                                bottomLine={true}
                                onClick={() => {
                                    if (state.canUpdate) this.bloc.requestModifyOutOrderNo();
                                }}
                            />
                        )}
                    </View>
                )}
                {isNanjing && (
                    <ListSettingItem
                        title={"采购方式"}
                        style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                        content={detail?.extendData?.purchaseModeName}
                        bottomLine={true}
                    />
                )}
                {isNanjing && detail?.extendData?.isYiBaoCollect && (
                    <ListSettingItem
                        title={"采购平台"}
                        style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                        content={detail?.extendData?.platformName}
                        bottomLine={true}
                    />
                )}
                <ListSettingItem
                    itemStyle={
                        state.canUpdate || (state.updateWithNeedRevoke && state.canAddMoreMedicine)
                            ? ListSettingItemStyle.expandIcon
                            : ListSettingItemStyle.normal
                    }
                    title={"验收人"}
                    style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                    content={detail?.inspectUser?.name ?? ""}
                    contentHint={state.canUpdate || (state.updateWithNeedRevoke && state.canAddMoreMedicine) ? "请填写" : ""}
                    bottomLine={true}
                    onClick={() => {
                        if (state.canUpdate || (state.updateWithNeedRevoke && state.canAddMoreMedicine))
                            this.bloc.requestModifyInspectUser();
                    }}
                />
                {state.canAddMoreMedicine ? (
                    <ListSettingEditItem
                        itemStyle={state.updateWithNeedRevoke ? ListSettingItemStyle.expandIcon : ListSettingItemStyle.normal}
                        editable={state.updateWithNeedRevoke}
                        title={"备注信息"}
                        style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                        content={state.comment}
                        contentHint={state.updateWithNeedRevoke ? "请填写" : ""}
                        bottomLine={true}
                        onChanged={(text) => {
                            this.bloc.requestModifyComment(text);
                        }}
                    />
                ) : (
                    <ListSettingItem
                        title={"备注信息"}
                        style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                        content={comment?.content}
                        bottomLine={true}
                    />
                )}
                <ListSettingItem
                    title={"附件"}
                    minTitleWidth={Sizes.dp57}
                    style={{ flex: 1, marginTop: Sizes.dp8, backgroundColor: Colors.white }}
                    contentStyle={{ flex: 1 }}
                    contentBuilder={() => {
                        return (
                            <ImageUpdateView
                                contentStyle={{ flex: undefined }}
                                ableUpdate={false}
                                showAddIcon={false}
                                imageSize={Sizes.dp46}
                                imageList={detail?.extendData?.attachments}
                            />
                        );
                    }}
                />
            </View>
        );
    }

    private _renderButtonsView(): JSX.Element {
        const buttons: JSX.Element[] = [];
        const state = this.bloc.currentState,
            detail = state.detail,
            isPurchaseWarehousing = detail?.isPurchaseWarehousing;
        if (isPurchaseWarehousing) {
            if (
                (state.needRevoke && !state.isRevokeEdit) ||
                (detail?.status == InventoryInDetailStatus.waitConfirm && userCenter.clinic?.isChainAdminClinic) ||
                (detail?.status == InventoryInDetailStatus.waitAgree && !userCenter.clinic?.isChainAdminClinic)
            ) {
                buttons.push(
                    <View key={"Revoke"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle1
                            text={"撤回"}
                            onClick={() => {
                                this.bloc.requestRevokeOrder();
                            }}
                        />
                    </View>
                );
                //下面是以前逻辑，在未完成之前可以对药品信息进行编辑
                // buttons.push(
                //     <View key={"EditRevoke"} style={{ flex: 2 }}>
                //         <ToolBarButtonStyle1
                //             text={"编辑"}
                //             onClick={() => {
                //                 this.bloc.requestOpenEditRevokeOrder();
                //             }}
                //         />
                //     </View>
                // );
            } else if (state.canAddMoreMedicine) {
                buttons.push(
                    <ToolBarButtonStyle1
                        key={"manual"}
                        text={"输入添加"}
                        onClick={() => this.bloc.requestAddMedicine(InventoryInType.manual)}
                    />
                );
                buttons.push(
                    <ToolBarButtonStyle1
                        key={"scan"}
                        text={"扫码入库"}
                        onClick={() => this.bloc.requestAddMedicine(InventoryInType.scan)}
                    />
                );
            } else if (detail?.status == InventoryInDetailStatus.waitAgree && userCenter.clinic?.isChainAdminClinic) {
                buttons.push(
                    <View key={"ReviewS"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"不通过"}
                            style={{ borderColor: Colors.P6, fontColor: Colors.R2, fontWeight: "normal" }}
                            onClick={() => {
                                this.bloc.requestReviewOrderPrev(false);
                            }}
                        />
                    </View>
                );
                buttons.push(
                    <View key={"ReviewF"} style={{ flex: 2 }}>
                        <ToolBarButtonStyle1
                            text={"通过"}
                            onClick={() => {
                                this.bloc.requestReviewOrderPrev(true);
                            }}
                        />
                    </View>
                );
            } else if (detail?.status == InventoryInDetailStatus.waitConfirm && !userCenter.clinic?.isChainAdminClinic) {
                buttons.push(
                    <View key={"ConfirmF"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"取消入库"}
                            style={{ borderColor: Colors.P6, fontColor: Colors.R2, fontWeight: "normal" }}
                            onClick={() => {
                                this.bloc.requestConfirmOrderPrev(false);
                            }}
                        />
                    </View>
                );
                buttons.push(
                    <View key={"ConfirmS"} style={{ flex: 2 }}>
                        <ToolBarButtonStyle1
                            text={"确认入库"}
                            onClick={() => {
                                this.bloc.requestConfirmOrderPrev(true);
                            }}
                        />
                    </View>
                );
            } else if (detail?.status == InventoryInDetailStatus.pass) {
                buttons.push(
                    <View key={"ReturnGoods"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"退货"}
                            onClick={() => {
                                this.bloc.requestQuickSkipReturnGoods();
                            }}
                        />
                    </View>
                );
            }
        } else {
            if (!userCenter.clinic?.isChainAdminClinic && detail?.status == InventoryInDetailStatus.waitConfirm) {
                buttons.push(
                    <View key={"ConfirmDelivery"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"确认出库"}
                            onClick={() => {
                                this.bloc.requestConfirmOrderPrev(true);
                            }}
                        />
                    </View>
                );
                buttons.push(
                    <View key={"CancelDelivery"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"取消出库"}
                            onClick={() => {
                                this.bloc.requestConfirmOrderPrev(false);
                            }}
                        />
                    </View>
                );
            } else if (userCenter.clinic?.isChainAdminClinic && detail?.status == InventoryInDetailStatus.waitConfirm) {
                buttons.push(
                    <View key={"Revocation"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"撤销出库"}
                            onClick={() => {
                                this.bloc.requestRevokeOrder();
                            }}
                        />
                    </View>
                );
            } else if (
                // 总店或者子店下从子店已创建的退货待确认出库 (总部 退货出库的待确认状态下显示)
                userCenter.clinic?.isChainAdminClinic &&
                detail?.status == InventoryInDetailStatus.waitConfirm
            ) {
                buttons.push(
                    <View key={"Withdraw"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"撤回"}
                            onClick={() => {
                                this.bloc.requestRevokeOrder();
                            }}
                        />
                    </View>
                );

                // 总部已创建的待审核出库 (总部 待审核)
            } else if (userCenter.clinic?.isChainAdminClinic && detail?.status == InventoryInDetailStatus.waitAgree) {
                buttons.push(
                    <View key={"Pass"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"通过"}
                            onClick={() => {
                                this.bloc.requestReviewOrderPrev(true);
                            }}
                        />
                    </View>
                );
                buttons.push(
                    <View key={"FailToPass"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"不通过"}
                            onClick={() => {
                                this.bloc.requestReviewOrderPrev(false);
                            }}
                        />
                    </View>
                );

                // 子店已创建的待审核出库 (子店 待审核)
            } else if (!userCenter.clinic?.isChainAdminClinic && detail?.status == InventoryInDetailStatus.waitAgree) {
                buttons.push(
                    <View key={"revokeBtn"} style={{ flex: 1 }}>
                        <ToolBarButtonStyle2
                            text={"撤销出库"}
                            onClick={() => {
                                this.bloc.requestRevokeOrder();
                            }}
                        />
                    </View>
                );
            }
        }
        if (!buttons.length) return <View />;
        return <ToolBar hideWhenKeyboardShow={true}>{buttons}</ToolBar>;
    }

    private _renderSummaryView(): JSX.Element {
        const state = this.bloc.currentState,
            detail = state.detail;
        const isPurchaseWarehousing = detail?.isPurchaseWarehousing; //是否为入库单

        return (
            <View
                style={[
                    { flexDirection: "row", justifyContent: "space-between" },
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp21, Sizes.listHorizontalMargin, Sizes.dp5),
                ]}
            >
                <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 })]}>
                    {`${isPurchaseWarehousing ? "入" : "出"}库药品`}
                </Text>
                <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }), { flex: 1, marginLeft: Sizes.dp16 }]}>
                    {`品种：${detail?.kindCount}，金额：${ABCUtils.inventoryPriceWithRMB({ price: detail?.amount ?? 0 })}`}
                    {/*{`品种：${detail?.kindCount}，数量：${NumberUtils.formatPriceToFixedWithoutZero(*/}
                    {/*    detail?.sum ?? 0*/}
                    {/*)}，含税金额：${ABCUtils.formatPrice(detail?.amount ?? 0)}`}*/}
                </Text>
            </View>
        );
    }

    private _renderGoodsListView(): JSX.Element {
        const state = this.bloc.currentState,
            list = state.detail?.list ?? [];
        const isPurchaseWarehousing = state.detail?.isPurchaseWarehousing;
        return (
            <View style={{ flex: 1 }}>
                {this._renderSummaryView()}
                <AbcListView
                    scrollEventThrottle={300}
                    numberOfRows={list.length}
                    dataSource={list}
                    getRowKey={(index) => index.toString()}
                    renderRow={(data) => (
                        <_InventoryListItem
                            detail={data}
                            isPurchaseWarehousing={isPurchaseWarehousing}
                            onChange={(detail) => this.bloc.requestModifyInventoryInItem(detail)}
                        />
                    )}
                />
            </View>
        );
    }

    renderContent(): JSX.Element | undefined {
        return (
            <View style={{ flex: 1 }}>
                {this._renderRefuseView()}
                {this._renderInvoiceBaseInfoView()}
                {this._renderGoodsListView()}
                {this._renderButtonsView()}
            </View>
        );
    }
}

interface _InventoryListItemProps {
    detail: InventoryInSheetPageDetailsList;
    isPurchaseWarehousing?: boolean;

    onChange?(detail: InventoryInSheetPageDetailsList): void;
}

class _InventoryListItem extends BaseComponent<_InventoryListItemProps> {
    private _renderGoodsBaseInfo(): JSX.Element {
        const goodsInfo = this.props.detail.goods;
        if (!goodsInfo) return <View />;
        return (
            <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp4 }]}>
                <Text numberOfLines={1} style={[TextStyles.t16MT1.copyWith({ lineHeight: Sizes.dp24 }), { flexShrink: 1 }]}>
                    {goodsInfo.displayName}
                </Text>
                <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { marginLeft: Sizes.dp8 }]}>
                    {goodsInfo.packageSpec}
                </Text>
                <Spacer />

                {/*{detail.__draftId && (*/}
                {/*  <IconFontView*/}
                {/*    name={"trash"}*/}
                {/*    color={Colors.T2}*/}
                {/*    size={Sizes.dp18}*/}
                {/*    style={{*/}
                {/*        marginRight: -Sizes.dp10,*/}
                {/*        paddingVertical: Sizes.dp10,*/}
                {/*        paddingHorizontal: Sizes.dp8,*/}
                {/*    }}*/}
                {/*    onClick={() => InventoryOutSheetPageBloc.fromContext(this.context).requestDeleteDetailListItem(item)}*/}
                {/*  />*/}
                {/*)}*/}
            </View>
        );
    }

    private _renderBatchInfo(): JSX.Element {
        const detail = this.props.detail,
            goodsInfo = detail.goods;
        if (!goodsInfo) return <View />;
        return (
            <View>
                <View style={[ABCStyles.rowAlignCenter, {}]}>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { flexShrink: 1 }]} numberOfLines={1}>
                        {/*{`批号 ${detail.batchNo ?? "--"}`}*/}
                        {!_.isEmpty(detail.batchNo) ? `批号 ${detail.batchNo ?? "--"}` : "--"}
                    </Text>
                </View>
                <View style={[ABCStyles.rowAlignCenter, { marginBottom: Sizes.dp8 }]}>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 })]}>
                        {`效期 ${TimeUtils.formatDate(detail.expiryDate, undefined, "--")}`}
                    </Text>
                    <Text style={[TextStyles.t14NT4.copyWith({ lineHeight: Sizes.dp20 }), { marginLeft: Sizes.dp12 }]}>
                        {`生产日期 ${TimeUtils.formatDate(detail.productionDate, undefined, "--")}`}
                    </Text>
                </View>
                <View style={[ABCStyles.rowAlignCenterSpaceBetween]}>
                    {/*<Text style={[TextStyles.t14NT1, { flexShrink: 1 }]}>{`售价 ${detail.sellingPrice}`}</Text>*/}
                    <Text style={[TextStyles.t14NT1, { flexShrink: 1 }]}>{`入库 ${detail.inStockCountDisplay}`}</Text>
                    <Text
                        style={[
                            TextStyles.t14NT1,
                            {
                                flexShrink: 1,
                                marginLeft: Sizes.dp16,
                            },
                        ]}
                    >
                        {`进价 ${detail.inStockCostDisplay}`}
                    </Text>
                    <Text style={[TextStyles.t14NT1, { flexShrink: 1, marginLeft: Sizes.dp16 }]}>
                        {`金额 ${!!detail.useTotalCostPrice ? detail.useTotalCostPrice : ABCUtils.paddingMoney(detail.calCostPriceTotal)}`}
                    </Text>
                </View>
            </View>
        );
    }

    private _renderReturnBatchInfo(): JSX.Element {
        const { detail } = this.props;
        /**
         * 是否展示批次
         * 总店 退货出库（待审核、待确认）或是 未选择批次时 不展示批次信息
         */
        let displayBatch = true;
        if (
            (userCenter.clinic?.isChainAdminClinic && detail.status == InventoryInDetailStatus.waitAgree) ||
            (userCenter.clinic?.isChainAdminClinic && detail.status == InventoryInDetailStatus.waitConfirm) ||
            !detail.stock?.batchId
        ) {
            displayBatch = false;
        }

        return (
            <View>
                <AbcView
                    key={detail.goods?.scrollKey}
                    onClick={() => {
                        // canOnClick ? InventoryOutSheetPageBloc.fromContext(this.context).requestOpenItem(item) : undefined; // 点击跳转到编辑窗口
                    }}
                >
                    <View style={[ABCStyles.rowAlignCenter, { paddingTop: Sizes.dp4 }]}>
                        <Text
                            style={[TextStyles.t14NT4, { lineHeight: Sizes.dp20, flexShrink: 1, marginRight: Sizes.dp16 }]}
                            numberOfLines={1}
                        >
                            {`厂家 ${
                                _.isEmpty(detail.goods?.manufacturer) && !detail.goods?.manufacturer ? "--" : detail.goods?.manufacturer
                            }`}
                        </Text>
                        {displayBatch && (
                            <Text style={[TextStyles.t14NT4, { lineHeight: Sizes.dp20, marginRight: Sizes.dp16 }]} numberOfLines={1}>
                                {`批次 ${detail.stock?.batchId ? detail.stock?.batchId : "--"}`}
                            </Text>
                        )}
                        <Text style={[TextStyles.t14NT4, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {`进价 ${ABCUtils.formatMoney(detail.packageCostPrice ?? 0, false)}/${
                                !_.isEmpty(detail.goods?.packageUnit) ? detail.goods?.packageUnit : detail.goods?.pieceUnit ?? "--"
                            }`}
                        </Text>
                    </View>

                    <View style={[ABCStyles.rowAlignCenter, { paddingTop: Sizes.dp4 }]}>
                        <Text
                            style={[TextStyles.t14NT4, { lineHeight: Sizes.dp20, marginRight: Sizes.dp16, flexShrink: 1 }]}
                            numberOfLines={1}
                        >
                            {`批号 ${_.isEmpty(detail?.batchNo) && !detail?.batchNo ? "--" : detail?.batchNo}`}
                        </Text>
                        <Text style={[TextStyles.t14NT4, { lineHeight: Sizes.dp20, marginRight: Sizes.dp16 }]} numberOfLines={1}>
                            {`效期 ${TimeUtils.formatDate(detail?.expiryDate, undefined, "--")}`}
                        </Text>
                    </View>

                    <View
                        style={[
                            ABCStyles.rowAlignCenter,
                            {
                                paddingTop: Sizes.dp8,
                            },
                        ]}
                    >
                        {detail.currentStockDisplay.length > 0 && (
                            <Text
                                style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20, marginRight: Sizes.dp16 }]}
                                numberOfLines={1}
                            >{`出库 ${detail.currentStockDisplay} `}</Text>
                        )}
                        <Text style={[TextStyles.t14NT1, { lineHeight: Sizes.dp20 }]} numberOfLines={1}>
                            {`金额 ${ABCUtils.inventoryPriceWithRMB({ price: detail.useTotalCostPrice ?? 0 })}`}
                        </Text>
                    </View>
                </AbcView>
            </View>
        );
    }

    render(): JSX.Element {
        const { detail, isPurchaseWarehousing } = this.props;
        return (
            <AbcView
                style={[
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp10),
                    { backgroundColor: Colors.white, marginBottom: Sizes.dpHalf },
                ]}
                onClick={() => {
                    isPurchaseWarehousing && this.props.onChange?.(detail);
                }}
            >
                {this._renderGoodsBaseInfo()}
                {isPurchaseWarehousing ? this._renderBatchInfo() : this._renderReturnBatchInfo()}
            </AbcView>
        );
    }
}
