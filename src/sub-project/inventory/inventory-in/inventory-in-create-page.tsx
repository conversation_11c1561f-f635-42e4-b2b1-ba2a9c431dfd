/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020/7/31
 *
 * @description
 */
import { SizedBox, Spacer, ToolBar, ToolBarButtonStyle1, UniqueKey } from "../../base-ui";
import { InventoryMedicineItem } from "../data/inventory-draft";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import React, { useContext } from "react";
import { InventoryInCreatePageBloc } from "./inventory-in-create-page-bloc";
import { ScrollView, Text, View } from "@hippy/react";
import { ABCStyles, Colors, Sizes, TextStyles } from "../../theme";
import { BaseBlocPage } from "../../base-ui/base-page";
import { ListSettingEditItem, ListSettingItem, ListSettingItemStyle } from "../../base-ui/views/list-setting-item";
import { ABCUtils } from "../../base-ui/utils/utils";
import _, { isNil } from "lodash";
import { GroupDivider } from "../../base-ui/divider-line";
import { RightArrowView } from "../../base-ui/iconfont/iconfont-view";
import { NumberUtils, TimeUtils } from "../../common-base-module/utils";
import { InventoryInType } from "./inventory-in-data/inventory-in-bean";
import { BlocBuilder } from "../../bloc";
import { AbcView } from "../../base-ui/views/abc-view";
import WillPopListener from "../../base-ui/views/will-pop-listener";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import abcI18Next from "../../language/config";
import { userCenter } from "../../user-center";
import { ImageUpdateView } from "../../base-ui/image-update-view";
import { AbcListView } from "../../base-ui/list/abc-list-view";

export interface InventoryCreatePageProps {
    draftId?: string;
    createType?: InventoryInType;
    inventoryMedicineItems?: InventoryMedicineItem[]; // 拍照添加
    imageUri?: string;
    imageWidth?: number;
    imageHeight?: number;
    originalImagePath?: string;
    from?: "PC" | "APP";
}

export class InventoryInCreatePage extends BaseBlocPage<InventoryCreatePageProps, InventoryInCreatePageBloc> {
    static show(options: {
        createType?: InventoryInType;
        draftId?: string;
        inventoryMedicineItems?: InventoryMedicineItem[]; // 拍照添加
        imageUri?: string;
        imageWidth?: number;
        imageHeight?: number;
        originalImagePath?: string;
        from?: "PC" | "APP";
    }): Promise<boolean> {
        return ABCNavigator.navigateToPage(<InventoryInCreatePage {...options} />);
    }

    bloc: InventoryInCreatePageBloc;

    constructor(props: InventoryCreatePageProps) {
        super(props);

        this.bloc = new InventoryInCreatePageBloc(props);
    }

    getAppBarTitle(): string {
        const state = this.bloc.currentState;
        return state.currentPharmacy?.type == PharmacyType.virtual ? "代煎代配入库单" : "新增入库";
    }

    getRightAppBarIcons(): JSX.Element[] {
        const state = this.bloc.currentState;
        if (state.isManual) {
            return [
                <AbcView key={1} onClick={() => this.bloc.requestSubmit()} airTestKey={"inventory_in_finish_btn"}>
                    <Text style={TextStyles.t16NM.copyWith({ color: state.hasAddMedicine ? Colors.mainColor : Colors.T2 })}>完成</Text>
                </AbcView>,
            ];
        } else {
            return [];
        }
    }

    getAppBar(): JSX.Element | undefined {
        return <BlocBuilder bloc={this.bloc} condition={() => true} build={() => super.getAppBar()} />;
    }

    onBackClick(): void {
        this.bloc.requestBackPage();
    }

    private _renderToolBar(): JSX.Element {
        const state = this.bloc.currentState,
            toolBarButtons: JSX.Element[] = [];
        if (state.isManual) {
            toolBarButtons.push(<ToolBarButtonStyle1 key={"manual"} text={"输入添加"} onClick={() => this.bloc.requestInputAdd()} />);
            if (state.currentPharmacy?.type != PharmacyType.virtual) {
                toolBarButtons.push(
                    <ToolBarButtonStyle1 key={"scan"} text={"扫码入库"} onClick={() => this.bloc.requestScanToInventory()} />
                );
            }
        } else {
            if (state.currentPharmacy?.type != PharmacyType.virtual) {
                toolBarButtons.push(
                    <ToolBarButtonStyle1 key={"scan"} text={"扫码入库"} onClick={() => this.bloc.requestScanToInventory()} />
                );
            }
            toolBarButtons.push(<ToolBarButtonStyle1 key={"submit"} text={"确认入库"} onClick={() => this.bloc.requestSubmit()} />);
        }
        toolBarButtons.push(<ToolBarButtonStyle1 key={"photo"} text={"拍照添加"} onClick={() => this.bloc.requestPhotoAdd()} />);
        if (!toolBarButtons.length) return <View />;
        return <ToolBar>{toolBarButtons}</ToolBar>;
    }

    renderContent(): JSX.Element {
        return (
            <View style={{ flex: 1 }}>
                <WillPopListener onWillPop={() => this.onBackClick()} />
                <ScrollView
                    ref={(ref) => {
                        ref?.scrollTo(0, 1, true);
                    }}
                    style={{ flex: 1 }}
                >
                    <_ClinicInfoView />
                    <_MedicineInfoListView />
                </ScrollView>
                {this._renderToolBar()}
            </View>
        );
    }
}

//顶部门店信息
const _ClinicInfoView = () => {
    const Context = useContext<InventoryInCreatePageBloc>(InventoryInCreatePageBloc.Context);
    const bloc = InventoryInCreatePageBloc.fromContext(Context);
    const { inited, clinic, draft, canModifyClinic, pharmacyInfoConfig, currentPharmacy, entryPharmacy } = bloc.currentState;

    if (!inited) return <View />;
    const isOpenMultiplePharmacy = pharmacyInfoConfig?.isOpenMultiplePharmacy;
    //加上!entryPharmacy?.name的条件，解决清理share preferences后，读取不到本地存储药房信息
    const isCanSelectPharmacy = currentPharmacy?.name == "全部库房" || !entryPharmacy?.name;
    // 当前机构所属地区是否是南京
    const isNanjing = userCenter.clinic?.isNanjingClinic;
    return (
        <View
            style={{
                backgroundColor: Colors.white,
                paddingHorizontal: Sizes.listHorizontalMargin,
            }}
        >
            <ListSettingItem
                itemStyle={canModifyClinic ? ListSettingItemStyle.expandIcon : undefined}
                title={"门店"}
                content={clinic?.displayName ?? ""}
                bottomLine={true}
                onClick={() => {
                    bloc.requestModifyClinicInfo();
                }}
            />
            {isOpenMultiplePharmacy && (
                <ListSettingItem
                    itemStyle={isCanSelectPharmacy ? ListSettingItemStyle.expandIcon : undefined}
                    title={"入库库房"}
                    contentHint={"选择入库库房"}
                    bottomLine={true}
                    content={entryPharmacy?.name == "全部库房" ? undefined : entryPharmacy?.name}
                    onClick={() => isCanSelectPharmacy && bloc.requestSelectMultiplePharmacy()}
                />
            )}
            <ListSettingItem
                itemStyle={ListSettingItemStyle.expandIcon}
                title={"供应商"}
                contentHint={"选择供应商"}
                bottomLine={true}
                content={draft?.supplier?.name}
                onClick={() => bloc.requestSelectSupplier()}
            />

            <ListSettingEditItem
                title={"随货单号"}
                contentHint={"输入随货单号"}
                bottomLine={true}
                content={draft?.outOrderNo}
                onChanged={(value) => bloc.requestUpdateOrderNo(value)}
            />
            {isNanjing && (
                <ListSettingItem
                    itemStyle={ListSettingItemStyle.expandIcon}
                    title={"采购方式"}
                    contentHint={"选择采购方式"}
                    bottomLine={true}
                    content={draft?.extendData?.purchaseModeName}
                    onClick={() => bloc.requestSelectPurchasingMode()}
                />
            )}
            {isNanjing && draft?.extendData?.isYiBaoCollect && (
                <ListSettingItem
                    itemStyle={ListSettingItemStyle.expandIcon}
                    title={"采购平台"}
                    contentHint={"选择采购平台"}
                    bottomLine={true}
                    content={draft?.extendData?.platformName}
                    onClick={() => bloc.requestSelectPurchasingPlatform()}
                />
            )}
            <ListSettingItem
                itemStyle={ListSettingItemStyle.expandIcon}
                title={"验收人"}
                style={[{ paddingRight: Sizes.listHorizontalMargin }]}
                content={draft?.inspectUser?.name ?? ""}
                contentHint={"选择验收人"}
                bottomLine={true}
                onClick={() => bloc.requestModifyInspectUser()}
            />
            <ListSettingEditItem
                title={"备注信息"}
                contentHint={"输入备注信息"}
                bottomLine={true}
                content={draft?.comment}
                onChanged={(value) => bloc.requestUpdateComment(value)}
            />
            <ListSettingItem
                title={"附件"}
                titleStyle={TextStyles.t16NT1}
                minTitleWidth={Sizes.dp57}
                style={{ flex: 1, marginTop: Sizes.dp8, backgroundColor: Colors.white }}
                contentStyle={{ flex: 1 }}
                contentBuilder={() => {
                    return (
                        <ImageUpdateView
                            contentStyle={{ flex: undefined }}
                            ableUpdate={true}
                            showAddIcon={true}
                            imageSize={Sizes.dp46}
                            imageList={draft?.extendData?.attachments ?? []}
                            onDelete={(index) => {
                                bloc.requestRemoveImage(index);
                            }}
                            onUpdate={() => {
                                bloc.requestAddImage();
                            }}
                        />
                    );
                }}
            />
        </View>
    );
};

const _MedicineInfoListView = () => {
    const Context = useContext<InventoryInCreatePageBloc>(InventoryInCreatePageBloc.Context);
    const bloc = InventoryInCreatePageBloc.fromContext(Context);
    const { inited, draft } = bloc.currentState;

    if (!inited || !draft) return <View />;

    if (_.isEmpty(draft.medicines))
        return (
            <View
                style={{
                    marginTop: Sizes.dp16,
                    paddingHorizontal: Sizes.dp16,
                    backgroundColor: Colors.white,
                    height: Sizes.dp100,
                    justifyContent: "center",
                }}
            >
                <Text style={[TextStyles.t14NT3, { alignSelf: "center" }]}>未添加药品</Text>
            </View>
        );

    if (!inited) return <View />;

    const _renderSummaryView = () => {
        let totalCost = 0;
        const medicineCount: Set<string | undefined> = new Set();
        draft.medicines.forEach((item) => {
            if (item.useCount == null || item.useUnitCostPrice == null) {
            }
            totalCost += (item.useCount ?? 0.0) * (item.useUnitCostPrice ?? 0.0);
            medicineCount.add(item.goodsInfo?.compareKey());
        });
        return (
            <View
                style={[
                    { flexDirection: "row", justifyContent: "space-between" },
                    Sizes.paddingLTRB(Sizes.listHorizontalMargin, Sizes.dp21, Sizes.listHorizontalMargin, Sizes.dp5),
                ]}
            >
                <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 })]}>入库药品</Text>
                <Text style={[TextStyles.t14NT2.copyWith({ lineHeight: Sizes.dp20 }), { flex: 1, marginLeft: Sizes.dp16 }]}>
                    {`品种：${medicineCount?.size}，金额：${ABCUtils.inventoryPriceWithRMB({ price: totalCost ?? 0 })}`}
                </Text>
            </View>
        );
    };

    return (
        <View>
            {_renderSummaryView()}
            <GroupDivider />
            {draft.medicines?.map((item) => (
                <_MedicineItemView key={item.goodsInfo?.scrollKey} item={item} />
            ))}
        </View>
    );
};

interface _MedicineItemViewProps {
    item: InventoryMedicineItem;
}

const _MedicineItemView: React.FC<_MedicineItemViewProps> = (props) => {
    const Context = useContext<InventoryInCreatePageBloc>(InventoryInCreatePageBloc.Context);
    const { useCount, useUnitCostPrice, goodsInfo, useUnit, batchNumber, productionDate, expiredTime, rowMatchInfo } = props.item;
    let totalPrice = "--";
    if (useUnitCostPrice != undefined && useCount != undefined) {
        totalPrice = ABCUtils.formatPrice(useUnitCostPrice * useCount);
    }
    // 是否是需要待修正的药品(只有拍照识别才会出现这个，扫码添加和手动输入不会有这个_isMatched)
    // !goodsInfo?.goodsId这个条件是为了兼容扫码到待修正药品后，直接返回保存成线上草稿后，数据中不会返回goodsInfo信息了，导致不能正确提示出来
    const isInvalid = !useCount || !useUnit || !goodsInfo?.goodsId;
    const _isNeedMatched = (!isNil(goodsInfo?._isMatched) && !goodsInfo?._isMatched) || !goodsInfo?.goodsId || isInvalid;
    const displayName = goodsInfo?.displayName ?? rowMatchInfo?.medicineCadn;
    const manufacturer = goodsInfo?.manufacturer ?? rowMatchInfo?.manufacturer;
    const specification = goodsInfo?.packageSpec ?? rowMatchInfo?.specification;
    return (
        <AbcView
            onClick={() => InventoryInCreatePageBloc.fromContext(Context).requestOpenItem(props.item)}
            style={[
                ABCStyles.bottomLine,
                {
                    backgroundColor: Colors.white,
                    paddingHorizontal: Sizes.listHorizontalMargin,
                    paddingVertical: Sizes.dp10,
                    justifyContent: "center",
                },
            ]}
        >
            <View style={ABCStyles.rowAlignCenter}>
                <Text
                    style={[
                        TextStyles.t16MB.copyWith({
                            lineHeight: Sizes.dp24,
                            color: !!goodsInfo?._showTipHint || _isNeedMatched ? Colors.Y1 : Colors.black,
                        }),
                        { marginRight: Sizes.dp8 },
                    ]}
                >
                    {displayName ?? ""}
                </Text>
                <Spacer />
                {_isNeedMatched && <Text style={TextStyles.t14NY1.copyWith({ lineHeight: Sizes.dp22 })}>{"待修正"}</Text>}
                <RightArrowView />
            </View>
            <SizedBox height={Sizes.dp4} />
            <View style={ABCStyles.rowAlignCenter}>
                <Text style={TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp20 })}>{specification ?? ""}</Text>
                <Text style={TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp20 })}>{manufacturer ?? ""}</Text>
                <Text style={TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp20 })}>{goodsInfo?.displayTypeName ?? ""}</Text>
            </View>

            <View style={{ flexDirection: "row" }}>
                <Text
                    style={[TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp20 }), { marginRight: Sizes.dp36, flexShrink: 1 }]}
                    numberOfLines={2}
                >
                    {`批号：${batchNumber ?? "--"}`}
                </Text>
                <Text style={[TextStyles.t12NT2.copyWith({ lineHeight: Sizes.dp20 })]}>
                    {`${productionDate ? TimeUtils.formatDate(productionDate) : "--"} ~ ${
                        expiredTime ? TimeUtils.formatDate(expiredTime) : "--"
                    }`}
                </Text>
            </View>
            <SizedBox height={Sizes.dp2} />
            <View style={ABCStyles.rowAlignCenterSpaceBetween}>
                <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp21 }), { flexShrink: 1 }]}>
                    {`进价：${abcI18Next.t("¥")}${
                        (useUnitCostPrice != null ? NumberUtils.formatMaxFixed(useUnitCostPrice) : null) ?? "--"
                    }/${useUnit}`}
                </Text>
                <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp21 }), { flexShrink: 1 }]}>{`数量：${useCount ?? ""}`}</Text>
                <Text style={[TextStyles.t13NT1.copyWith({ lineHeight: Sizes.dp21 }), { flexShrink: 1 }]}>
                    {`金额：${abcI18Next.t("¥")}${totalPrice}`}
                </Text>
            </View>
        </AbcView>
    );
};
