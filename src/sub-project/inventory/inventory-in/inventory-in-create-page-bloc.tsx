/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-08-1
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { EventName } from "../../bloc/bloc";
import { InventoryMedicineItem, SupplierItem } from "../data/inventory-draft";
import { InventoryInQrcodeScanPage } from "./inventory-in-qrcode-scan-page";
import _, { isNil } from "lodash";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { GoodsAgent, InventoryInStatus, PharmacyListItem, SupplierItemStatus } from "../../data/goods/goods-agent";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { Clinic, ClinicEmployee, userCenter } from "../../user-center/user-center";
import { InventoryInMedicineEditPage, InventoryMedicineEditResult } from "./inventory-in-medicine-edit-page";
import { showImageConfirmDialog } from "../../base-ui/dialog/image-confirm-dialog";
import { Toast } from "../../base-ui/dialog/toast";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { errorSummary, errorToStr, UUIDGen } from "../../common-base-module/utils";
import { Permission, PermissionStatus, PermissionType } from "../../common-base-module/permission/permission";
import { InventoryCreatePageProps } from "./inventory-in-create-page";
import { InventoryInDraftManage } from "./inventory-in-data/inventory-in-draft-manage";
import { InventoryDraft, InventoryInToOrgan, InventoryInType } from "./inventory-in-data/inventory-in-bean";
import { InventoryInConst } from "./inventory-in-data/inventory-in-const";
import { Employee, GoodsInfo, GoodsSubType, GoodsType, OssUpdateModules, AttachmentItem } from "../../base-business/data/beans";
import { InventoryMedicineSearchPage } from "../Inventory-medicine-search-page";
import { InventoryInUtils } from "./utils/inventory-in-utils";
import { SupplierSelectList } from "./inventory-in-views/views";
import { InventoryInAgent, InventoryInRsp, PostOrderDraftAiPictureRsp } from "./inventory-in-data/inventory-in-agent";
import { ClinicAgent } from "../../base-business/data/clinic-agent";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { showOptionsBottomSheet } from "../../base-ui/dialog/bottom_sheet";
import { ignore } from "../../common-base-module/global";
import { InventoryAgent } from "../data/inventory-agent";
import { PharmacyType } from "../../charge/data/charge-bean-air-pharmacy";
import { ABCApiError } from "../../net";
import { ExtendData, InventoryApiErrorCode, InventoryClinicConfig, PlatFormType, PurchaseType } from "../data/inventory-bean";
import { ScrollView, View } from "@hippy/react";
import { AbcImagePicker } from "../../base-business/image-picker/abc-image-picker";
import FileUtils from "../../common-base-module/file/file-utils";
import { File } from "../../common-base-module/file/file";
import { AbcImageRecoginition } from "../../base-ui/abc-image-recognition/abc-image-recogniation";
import { OssConfigs } from "../../base-ui/oss/oss-config";
import { FileUploader } from "../../base-business/file-uploader/file-uploader";
import { OssImageUtils } from "../../base-business/file-uploader/oss-image-utils";
import { PhotoRecognitionResultPageProps } from "../inventory-photo-goods-receipt/photo-recognition-result-page";

export class State {
    employee?: ClinicEmployee;
    clinic?: Clinic;
    clinicsList?: Clinic[];

    inited = false;

    suppliers?: SupplierItem[];

    draft: InventoryDraft = new InventoryDraft();
    _draft?: InventoryDraft;

    createType?: InventoryInType;

    scrollKeyIndex?: number;

    stockInChainReview?: boolean;
    clinicEmployees: Employee[] = [];

    /**
     * 筛选项的药房
     */
    currentPharmacy?: PharmacyListItem;
    //入库药房(存储新建入库单时选择的药房)
    entryPharmacy?: PharmacyListItem;
    //goods配置相关信息--此处关注多药房
    pharmacyInfoConfig?: InventoryClinicConfig;

    pharmacyList?: PharmacyListItem[];

    focusItemKey?: string;
    currentFocusItem?: InventoryMedicineItem;

    draftAiPictureId?: string; // AI拍照入库单草稿ID
    draftAiPicture?: PostOrderDraftAiPictureRsp; // AI拍照入库单草稿信息

    /**
     * @desc 扫码入库
     */
    get isScan(): boolean {
        return this.createType == InventoryInType.scan;
    }

    get isPhoto(): boolean {
        return this.createType == InventoryInType.photo;
    }

    get isManual(): boolean {
        return this.createType == InventoryInType.manual || this.createType == InventoryInType.onlineDraft || this.isPhoto;
    }

    get canModifyClinic(): boolean {
        return !!userCenter.clinic?.isChainAdminClinic;
    }

    get hasAddMedicine(): boolean {
        return !!this.draft.medicines.length;
    }

    get hasChange(): boolean {
        return !_.isEqual(this.draft, this._draft);
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollSameViewState extends State {
    static fromState(state: State): ScrollSameViewState {
        const newState = new ScrollSameViewState();
        Object.assign(newState, state);
        return newState;
    }
}

export class ScrollToMedicineState extends State {
    medicineIndex: number;

    constructor(medicineIndex: number) {
        super();
        this.medicineIndex = medicineIndex;
    }

    static fromState(state: State, medicineIndex: number): ScrollToMedicineState {
        const newState = new ScrollToMedicineState(medicineIndex);
        Object.assign(newState, state);
        return newState;
    }
}

export class InventoryInCreatePageBloc extends Bloc<_Event, State> {
    static fromContext(context: InventoryInCreatePageBloc): InventoryInCreatePageBloc {
        return context;
    }

    constructor(options: InventoryCreatePageProps) {
        super();
        this._draftId = options.draftId;
        if (this._draftId == InventoryInConst.inventoryInTypeScanDraftFileName) {
            this._createType = InventoryInType.scan;
        } else if (options.createType == InventoryInType.photo) {
            this._createType = InventoryInType.photo;
            this._inventoryMedicineItems = options.inventoryMedicineItems ?? [];
            this._imageUri = options.imageUri;
            this._imageWidth = options.imageWidth;
            this._imageHeight = options.imageHeight;
            this._originalImagePath = options.originalImagePath;
            this._from = options.from;
        } else {
            this._createType = options.createType ?? InventoryInType.scan;
        }
        this.draftManager = InventoryInDraftManage.inventoryInDraftManageInstance;
        if (this._draftId) {
            this._isEditDraft = true;
        }
        this.dispatch(new _EventInit());
    }

    private readonly draftManager: InventoryInDraftManage;
    private readonly _createType: InventoryInType;
    private _draftId?: string;
    private _isEditDraft = false;
    private _inventoryMedicineItems?: InventoryMedicineItem[];
    private _imageUri?: string;
    private _imageWidth?: number;
    private _imageHeight?: number;
    private _originalImagePath?: string;
    private _from?: "PC" | "APP";

    private _innerState!: State;
    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    get isOnlineDraft(): boolean {
        return this._createType == InventoryInType.onlineDraft;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventSelectSupplier, this._mapEventSelectSuppler);
        map.set(_EventInputAdd, this._mapEventInputAdd);
        map.set(_EventScanToInventory, this._mapEventScanToInventory);
        map.set(_EventOpenItemTap, this._mapEventOpenItemTap);
        map.set(_EventSubmit, this._mapEventSubmit);
        map.set(_EventUpdateComment, this._mapEventUpdateComment);
        map.set(_EventUpdateOrderNo, this._mapEventOrderNo);
        map.set(_EventModifyInspectUser, this._mapEventModifyInspectUser);
        map.set(_EventClear, this._mapEventClear);
        map.set(_EventDeleteDraft, this._mapEventDeleteDraft);

        //new
        map.set(_EventAddToMedicineList, this._mapEventAddToMedicineList);
        map.set(_EventDeleteMedicine, this._mapEventDeleteMedicine);
        map.set(_EventModifyClinicInfo, this._mapEventModifyClinicInfo);
        map.set(_EventBackPage, this._mapEventBackPage);
        map.set(_EventSelectMultiplePharmacy, this._mapEventSelectMultiplePharmacy);
        map.set(_EventSelectPurchasingMode, this._mapEventSelectPurchasingMode);
        map.set(_EventSelectPurchasingPlatform, this._mapEventSelectPurchasingPlatform);
        map.set(_EventScrollToMedicine, this._mapEventScrollToMedicine);

        map.set(_EventPhotoAdd, this._mapEventPhotoAdd);
        map.set(_EventAddImage, this._mapEventAddImage);
        map.set(_EventRemoveImage, this._mapEventRemoveImage);
        return map;
    }

    private _saveDraft(): void {
        this.draftManager.saveDraft(this.innerState.draft);
    }

    //选择供应商
    public requestSelectSupplier(): void {
        this.dispatch(new _EventSelectSupplier());
    }

    //扫码入库
    public requestScanToInventory(): void {
        this.dispatch(new _EventScanToInventory());
    }

    //手动输入
    public requestInputAdd(): void {
        this.dispatch(new _EventInputAdd());
    }

    //点击打开一条药品
    public requestOpenItem(item: InventoryMedicineItem, createType?: InventoryInType): void {
        this.dispatch(new _EventOpenItemTap(item, createType));
    }

    //确认入库
    public requestSubmit(): void {
        this.dispatch(new _EventSubmit());
    }

    //拍照添加
    public requestPhotoAdd(): void {
        this.dispatch(new _EventPhotoAdd());
    }

    //添加图片
    public requestAddImage(): void {
        this.dispatch(new _EventAddImage());
    }

    //删除图片
    public requestRemoveImage(index: number): void {
        this.dispatch(new _EventRemoveImage(index));
    }

    //更新备注信息
    public requestUpdateComment(value: string): void {
        this.dispatch(new _EventUpdateComment(value));
    }

    //随货单号
    public requestUpdateOrderNo(value: string): void {
        this.dispatch(new _EventUpdateOrderNo(value));
    }

    public requestModifyInspectUser(): void {
        this.dispatch(new _EventModifyInspectUser());
    }

    //清空
    public requestClear(): void {
        this.dispatch(new _EventClear());
    }

    public requestDeleteDraft(): void {
        this.dispatch(new _EventDeleteDraft());
    }

    public requestModifyClinicInfo(): void {
        this.dispatch(new _EventModifyClinicInfo());
    }

    public requestBackPage(): void {
        this.dispatch(new _EventBackPage());
    }

    requestSelectMultiplePharmacy(): void {
        this.dispatch(new _EventSelectMultiplePharmacy());
    }

    /**
     * 滚动到指定药品位置
     * @param medicineIndex 药品在 draft.medicines 数组中的下标
     */
    requestScrollToMedicine(medicineIndex: number): void {
        this.dispatch(new _EventScrollToMedicine(medicineIndex));
    }
    requestSelectPurchasingMode(): void {
        this.dispatch(new _EventSelectPurchasingMode());
    }
    requestSelectPurchasingPlatform(): void {
        this.dispatch(new _EventSelectPurchasingPlatform());
    }

    private async *_mapEventInit(/*ignore: _EventInit*/): AsyncGenerator<State> {
        //多药房相关配置
        this.innerState.pharmacyInfoConfig = userCenter.inventoryClinicConfig;
        await GoodsAgent.getPharmacyList()
            .then((rsp) => {
                this.innerState.pharmacyList = rsp?.rows;
            })
            .catchIgnore();
        /**
         * 获取药房type
         */
        this.innerState.currentPharmacy = InventoryAgent.getCurrentPharmacy();
        this.innerState.entryPharmacy = _.cloneDeep(this.innerState.currentPharmacy);

        //拉取门店列表
        await ClinicAgent.getChainClinics()
            .then((rsp) => {
                this.innerState.clinicsList = rsp.map((item) => {
                    if (item.chainAdmin === 1) {
                        item.shortName = "总部";
                        item.name = "总部";
                    }
                    return JsonMapper.deserialize(Clinic, { ...item, clinicId: item.id });
                });
            })
            .catchIgnore();

        /**
         * 拉取诊所库存相关配置
         */
        ClinicAgent.getInventoryChainConfig().then((config) => {
            this.innerState.stockInChainReview = !!config.chainReview?.stockInChainReview;
        });

        const innerState = this.innerState;
        innerState.createType = this._createType;
        innerState.employee = userCenter.employee;
        innerState.clinic = userCenter.clinic;
        innerState.inited = true;
        //获取当前门店所有员工信息
        ClinicAgent.getClinicAllEmployeeList().then((rsp) => {
            this.innerState.clinicEmployees = rsp || [];
            innerState.draft.inspectUser = {
                id: innerState.employee?.id,
                name: innerState.employee?.name,
            };
            this.update();
        });
        if (this._draftId && !this.isOnlineDraft) {
            innerState.draft = await this.draftManager.loadInventoryInDraft(this._draftId);
            /**
             * 手动触发生成scrollKey
             * 解决：草稿clone问题
             */
            innerState.draft.medicines.forEach((item) => ignore(item.goodsInfo?.scrollKey));
            innerState._draft = _.cloneDeep(innerState.draft);
            // 如果有传入的药品列表，需要将药品列表添加到草稿中
            if (this._inventoryMedicineItems) {
                innerState.draft = new InventoryDraft();
                innerState.draft?.medicines.push(...this._inventoryMedicineItems);
                innerState.draft.__draftId = this._draftId;
                innerState.draft.medicines = this._inventoryMedicineItems;
                if (this._originalImagePath) {
                    try {
                        // 检查文件是否存在
                        if (!(await FileUtils.fileExists(this._originalImagePath))) {
                            console.error("文件不存在:", this._originalImagePath);
                            return;
                        }

                        // 获取并验证OSS配置
                        const ossConfig = OssConfigs.chatAudioOssConfig();
                        if (!ossConfig?.bucket || !ossConfig?.root) {
                            console.error("OSS配置不完整:", ossConfig);
                            return;
                        }

                        // 上传文件到OSS
                        const fileExt = FileUtils.getFileExt(this._originalImagePath);
                        const ossPath = `${ossConfig.root}/${OssUpdateModules.GOODS}/${UUIDGen.generate()}${fileExt}`;
                        const uploadResult = await FileUploader.uploadFile(this._originalImagePath, ossConfig.bucket, ossPath);

                        // 验证上传结果
                        if (!uploadResult?.filePath || !uploadResult?.url) {
                            console.error("上传结果不完整:", uploadResult);
                            return;
                        }

                        // 创建并设置附件
                        innerState.draft.extendData = JsonMapper.deserialize(ExtendData, {
                            attachments: [
                                {
                                    fileName: uploadResult.filePath.split("/").pop(),
                                    url: uploadResult.url,
                                    imageHeight: this._imageHeight,
                                    imageWidth: this._imageWidth,
                                },
                            ],
                        });

                        // 标记草稿已更改
                        innerState.draft.hasChanged = true;
                        this.update();
                    } catch (error) {
                        console.error("处理图片时发生错误:", error);
                    }
                }
            }
            innerState.clinic = innerState.draft.clinic ?? innerState.clinic;
        } else {
            innerState.draft = new InventoryDraft();
            if (!!this._draftId && this.isOnlineDraft) {
                const onlineDraft = await InventoryInAgent.getStockInDraftDetail(this._draftId).catchIgnore();
                if (onlineDraft) {
                    innerState.draft = onlineDraft?.localDraftDetail;
                    innerState.entryPharmacy = onlineDraft?.pharmacy;
                }
                innerState._draft = _.cloneDeep(innerState.draft);

                //判断当前草稿是否有本地草稿
                const list = await InventoryInDraftManage.inventoryInDraftManageInstance.getAllDrafts();
                const localDraft = list.find((it) => it.__draftId == this._draftId);
                if (!!list.length && !!localDraft) {
                    const result = await showQueryDialog("提示", "当前存在未处理草稿，是否恢复？");
                    if (result == DialogIndex.positive) {
                        innerState.draft = localDraft;
                    }
                }
            } else {
                innerState.draft.clinicId = innerState.clinic?.clinicId;
                innerState.draft.__draftId =
                    this._createType == InventoryInType.scan
                        ? InventoryInConst.inventoryInTypeScanDraftFileName
                        : InventoryInDraftManage.generateDraftId();
                this._draftId = innerState.draft.__draftId;
                innerState._draft = _.cloneDeep(innerState.draft);
            }
        }

        //是否自动拉起扫码
        if (this._createType == InventoryInType.scan && !innerState.draft.medicines.length) {
            this.dispatch(new _EventScanToInventory(true));
        }

        this._loadSupplier().then();

        yield innerState.clone();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }

        yield this.innerState.clone();
    }

    private async *_mapEventSelectSuppler(/*ignore: _EventSelectSupplier*/): AsyncGenerator<State> {
        const innerState = this.innerState;

        const result = await SupplierSelectList.show({
            supplier: innerState.draft?.supplier,
            pharmacyType: this.innerState.currentPharmacy?.type ?? 0,
        });

        if (result) {
            innerState.draft!.supplier = result;
            innerState.draft!.supplier.supplierId = result.supplierId ?? result.id;
            innerState.draft!.hasChanged = true;
            this._saveDraft();
            yield innerState.clone();
        }
    }

    private async *_mapEventInputAdd(): AsyncGenerator<State> {
        const currentPharmacy = this.innerState.entryPharmacy;
        const params = {
            withStock: "1",
            onlyStock: "",
            disable: 0,
            clinicId: this.innerState.clinic?.clinicId,
            inorderConfig: "0",
            types: currentPharmacy?.type == PharmacyType.virtual ? [GoodsType.medicine] : undefined,
            subTypes: currentPharmacy?.type == PharmacyType.virtual ? [GoodsSubType.medicineChinese] : undefined,
            pharmacyNo: currentPharmacy?.no,

            // types: _.isNil(currentPharmacy?.pharmacyGoodsType?.type) ? undefined : [currentPharmacy!.pharmacyGoodsType!.type],
            // subTypes: _.isNil(currentPharmacy?.pharmacyGoodsType?.subType) ? undefined : [currentPharmacy!.pharmacyGoodsType!.subType],
            // cMSpec: _.isNil(currentPharmacy?.pharmacyGoodsType?.cMSpec) ? undefined : [currentPharmacy!.pharmacyGoodsType!.cMSpec],
        };

        if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && _.isUndefined(currentPharmacy?.no)) {
            await Toast.show("请选择入库药房", { warning: true });
            return;
        }

        const goodsInfo = await ABCNavigator.navigateToPage<GoodsInfo | undefined>(
            <InventoryMedicineSearchPage searchParams={params} callback={(goodsInfo) => ABCNavigator.pop(goodsInfo)} />
        );

        if (!goodsInfo || !goodsInfo.id) return;

        // 创建新的药品项，确保包含所有必要字段
        const newItem = InventoryInUtils.goodsInfoTransToInventoryMedicineItem(goodsInfo);
        // 确保 goodsInfo 是新的实例
        if (newItem.goodsInfo) {
            const original = newItem.goodsInfo;
            newItem.goodsInfo = Object.assign(new GoodsInfo(), original);
        }

        // 更新 _showTipHint 状态
        const isYiBaoCollectValid =
            !isNil(newItem.extendData?.emergencyFlag) && !!newItem.extendData?.erpGoodsId && !!newItem.extendData?.erpOrderItemId;
        const isNormalItemValid = newItem.useCount && newItem.useUnit;
        const newShowTipHint = !(isYiBaoCollectValid || isNormalItemValid);

        if (newItem.goodsInfo) {
            newItem.goodsInfo._showTipHint = newShowTipHint;
        }

        this.dispatch(new _EventOpenItemTap(newItem));
    }
    // 添加或更新药品项到入库单
    private async *_mapEventAddToMedicineList(event: _EventAddToMedicineList): AsyncGenerator<State> {
        console.log("处理添加/更新药品:", {
            originalItem: event.originalItem,
            newItem: event.item,
            isReplaced: event.item.goodsInfo?._isGoodsReplaced,
            draft: this.innerState.draft.medicines,
        });

        // 1. 如果是替换操作
        if (event.originalItem && event.item.goodsInfo?._isGoodsReplaced) {
            const originIndex = this.innerState.draft.medicines.findIndex((item) => {
                if (event.originalItem?.goodsInfo?.scrollKey) return item.goodsInfo?.scrollKey === event.originalItem?.goodsInfo?.scrollKey;
                // TODO 如果是保存草稿后再打开，进行替换，此时goodsInfo是不存在的，根据哪个值判断待定
                return item.id === event.originalItem?.id;
            });
            this.innerState.draft.medicines.splice(originIndex, 1, event.item);
        }
        // 5. 普通更新或添加操作
        else {
            const index = this.innerState.draft.medicines.findIndex((e) => e.goodsInfo?.scrollKey === event.item.goodsInfo?.scrollKey);
            if (index >= 0) {
                this.innerState.draft.medicines.splice(index, 1, event.item);
            } else {
                this.innerState.draft.medicines.push(event.item);
            }
            this.innerState.draft.medicines?.forEach((item) => {
                if (!isNil(item.extendData?.emergencyFlag) && !!item.extendData?.erpGoodsId && !!item.extendData?.erpOrderItemId) {
                    item.goodsInfo = item?.goodsInfo ?? new GoodsInfo();
                    item.goodsInfo._showTipHint = false;
                }
            });
        }

        // 6. 更新草稿
        this.innerState.draft.lastModifyTime = new Date();
        await this._saveDraft();
        this.innerState.currentFocusItem = event.item;
        yield ScrollSameViewState.fromState(this.innerState);
    }

    private async *_mapEventDeleteMedicine(event: _EventDeleteMedicine): AsyncGenerator<State> {
        const index = this.innerState.draft.medicines.findIndex((e) => e == event.item);

        if (index >= 0) {
            this.innerState.draft.medicines.splice(index, 1);
            this.innerState.draft.lastModifyTime = new Date();
            this._saveDraft();
            this.update();
        }
    }

    private async *_mapEventScanToInventory(event: _EventScanToInventory): AsyncGenerator<State> {
        const hasPermissions = await Permission.checkPermission(PermissionType.camera, true);
        if (hasPermissions == PermissionStatus.denied || hasPermissions == PermissionStatus.permanentlyDenied) {
            await Toast.show("无相机使用权限", { warning: true });
            return {
                error: "无相机使用权限",
            };
        }

        // 扫码入库进入时，不直接提示入库药房末选择
        if (!event.isFromScanEnter) {
            if (this.innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && _.isUndefined(this.innerState.entryPharmacy?.no)) {
                await Toast.show("请选择入库药房", { warning: true });
                return;
            }
        }

        InventoryInQrcodeScanPage.show({
            detail: this.innerState.draft,
            onOneItemAdded: (item) => {
                this.dispatch(new _EventAddToMedicineList(item));
            },
            onConfirmInStock: () => {
                ABCNavigator.pop();
            },
            pharmacyNo: this.innerState.entryPharmacy?.no,
        })
            .then((rsp) => {
                if (rsp) {
                    //continue
                    this.dispatch(new _EventScanToInventory());
                }
            })
            .catch((error) => {
                if (error && error.code === 401) {
                    return; // AbcNetDelegate中已做处理 这里选择忽略
                }
            })
            .finally(() => {
                this.update();
            });
    }

    private async *_mapEventOpenItemTap(event: _EventOpenItemTap): AsyncGenerator<State> {
        // 1. 创建原始药品的深拷贝，避免修改原始引用
        const originalItemCopy = _.cloneDeep(event.item);

        // 2. 使用副本来进行编辑
        const result = await InventoryInMedicineEditPage.editItem(originalItemCopy, {
            isYiBaoCollect: this.innerState.draft.extendData?.isYiBaoCollect,
            isEditReceiptInfo: this.innerState.createType === InventoryInType.photo,
        }).catchIgnore();

        if (_.isUndefined(result)) return;

        if (result.action == InventoryMedicineEditResult.actionDelete) {
            // 删除操作，使用原始引用
            this.dispatch(new _EventDeleteMedicine(event.item)).then();
        } else if (result.action == InventoryMedicineEditResult.actionChanged) {
            if (result.newItem?.goodsInfo?._isGoodsReplaced) {
                // 替换操作，传入原始药品引用
                this.dispatch(new _EventAddToMedicineList(result.newItem, event.item)).then();
            } else {
                // 普通更新操作
                this.dispatch(new _EventAddToMedicineList(result.newItem!)).then();
            }
        }
    }

    /**
     * 验证药品项是否填写完整
     * @param medicines 药品项列表
     * @returns 验证结果
     */
    private _validateMedicineItems(medicines: InventoryMedicineItem[]): {
        isValid: boolean; // 是否验证通过
        invalidCount: number; // 无效药品项数量
        firstInvalidItemId?: string; // 第一个无效药品项的id
    } {
        let invalidCount = 0;
        let firstInvalidItemId: string | undefined;

        for (const item of medicines) {
            const isInvalid = !item.useCount || !item.useUnit || !item.goodsInfo?.goodsId;

            if (isInvalid) {
                invalidCount++;
                if (!firstInvalidItemId) {
                    firstInvalidItemId = item.goodsInfo?.id;
                }
            }
        }

        return {
            isValid: invalidCount === 0,
            invalidCount,
            firstInvalidItemId,
        };
    }

    private async *_mapEventSubmit(/*ignore: _EventSubmit*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        if (!innerState.hasAddMedicine) return;
        if (innerState.pharmacyInfoConfig?.isOpenMultiplePharmacy && _.isUndefined(innerState.entryPharmacy?.no)) {
            await Toast.show("请选择入库药房", { warning: true });
            return;
        }
        if (innerState.draft!.supplier == undefined) {
            await Toast.show("请选择供应商", { warning: true });
            return;
        }
        if (innerState.draft!.inspectUser == undefined) {
            await Toast.show("请选择验收人", { warning: true });
            return;
        }

        if (_.isEmpty(innerState.draft?.medicines)) {
            await Toast.show("未添入库项", { warning: true });
            return;
        }
        // 验证药品项
        const validationResult = this._validateMedicineItems(innerState.draft.medicines);
        if (!validationResult.isValid) {
            if (validationResult.firstInvalidItemId) {
                innerState.focusItemKey = `medicine_${validationResult.firstInvalidItemId}`;
            }
            const result = await showQueryDialog(
                "入库信息错误",
                `共有 ${validationResult.invalidCount} 种商品信息不完整，请修正后提交`,
                "确定",
                "取消"
            );
            if (result == DialogIndex.positive) {
                // 滚动到待修正项
                yield ScrollSameViewState.fromState(innerState);
            }
            this.update();
            return;
        }
        // 南京地区-采购方式必填（采购方式为医保集采下，采购平台必填）
        if (userCenter.clinic?.isNanjingClinic) {
            if (isNil(innerState.draft.extendData?.purchaseType)) {
                await Toast.show("请选择采购方式", { warning: true });
                return;
            }
            if (innerState.draft.extendData?.isYiBaoCollect && isNil(innerState.draft.extendData?.platformType)) {
                await Toast.show("请选择采购平台", { warning: true });
                return;
            }
            // 医保采集方式下，药品信息（平台产品编码、应急采购、订单明细ID必填）
            if (innerState.draft.extendData?.isYiBaoCollect) {
                innerState.draft.medicines?.map((item) => {
                    if (!item?.extendData?.erpOrderItemId || isNil(item.extendData?.emergencyFlag) || !item.extendData?.erpGoodsId) {
                        item.goodsInfo = item.goodsInfo ?? new GoodsInfo();
                        item.goodsInfo._showTipHint = true;
                        this.update();
                    }
                });
                const isExistUndefinedItem = innerState.draft?.medicines?.some((t) => t.goodsInfo?._showTipHint);
                if (!!isExistUndefinedItem) {
                    await Toast.show("某些项存在平台产品编码、应急采购、订单明细ID未填", { warning: true });
                    return;
                }
            }
        }

        // 解决clinicId未传问题，目前只在单店有反馈过，但是本地暂时未复现：
        if (!this.innerState.draft?.clinicId && userCenter.clinic?.viewMode == 1) {
            this.innerState.draft.clinicId = userCenter.clinic?.clinicId;
        }
        const req = this.innerState.draft!.toInventoryReq(this.isOnlineDraft);
        if (req.clinicId != userCenter.clinic?.clinicId) {
            //总店入子店库
            const result = await showQueryDialog("", `入库单需要 ${innerState.clinic?.displayName} 确认`);
            if (result == DialogIndex.positive) {
                const dialog = new LoadingDialog("正在提交入库单");
                dialog.show();
                try {
                    await InventoryInAgent.addInventory({
                        ...req,
                        clinicId: innerState.clinic?.clinicId,
                        pharmacyNo: innerState.currentPharmacy?.no ?? 0,
                    });
                } catch (error) {
                    if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode) {
                        dialog.hide().then(() => {
                            //处理12100异常code
                            const { detail } = error?.detail?.error ?? {};
                            showConfirmDialog(
                                `提交失败：${detail?.errorTitle ?? ""}`,
                                <ScrollView>
                                    <View>
                                        {detail.errorList
                                            .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                            .join("\n")}
                                    </View>
                                </ScrollView>
                            );
                        });
                    } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.submitted) {
                        showQueryDialog("提示", error?.detail?.error?.message).then((index) => {
                            if (index == DialogIndex.positive) {
                                InventoryInAgent.addInventory({ ...req, pharmacyNo: innerState.currentPharmacy?.no ?? 0, forceSubmit: 1 })
                                    .then(() => {
                                        if (this._draftId) this.draftManager.removeDraft(this._draftId);
                                        Toast.show("创建成功", { success: true }).then(() => {
                                            ABCNavigator.pop();
                                        });
                                    })
                                    .catch((error: any) => {
                                        showQueryDialog("提示", errorToStr(error));
                                    });
                                ABCNavigator.pop(true);
                            } else {
                                if (this._draftId) this.draftManager.removeDraft(this._draftId);
                                ABCNavigator.pop();
                            }
                        });
                    } else {
                        await dialog.fail("入库失败:" + errorSummary(error));
                    }
                    return;
                }

                await dialog.hide();
                innerState.draft!.hasSubmit = true;
                if (this._draftId) await this.draftManager.removeDraft(this._draftId);
                ABCNavigator.pop(true); //true表示 本次创建了入库单
            }
        } else {
            /**
             * @description 是否需要审核在提交前进行提示
             */
            if (innerState.stockInChainReview && userCenter.clinic?.isChainSubClinic) {
                const result = await showQueryDialog("", "确定后将发送给总部审核，审核通过后将立即更新库存");
                if (result != DialogIndex.positive) return;
            } else {
                const result = await showQueryDialog("", "入库后将立即更新系统库存");
                if (result != DialogIndex.positive) return;
            }
            const dialog = new LoadingDialog("正在提交入库单");
            dialog.show();
            let rsp: InventoryInRsp;
            try {
                rsp = await InventoryInAgent.addInventory({
                    ...req,
                    pharmacyNo: innerState.entryPharmacy?.no ?? 0,
                    pharmacyType: innerState.entryPharmacy?.type,
                });
            } catch (error) {
                if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.goodsStockSummaryErrorCode) {
                    //处理12100异常code
                    const { detail } = error?.detail?.error ?? {};
                    await showConfirmDialog(
                        `提交失败：${detail?.errorTitle ?? ""}`,
                        <ScrollView>
                            <View>
                                {detail.errorList
                                    .map((it: { name: string; msgs: string[] }) => `${it.name}:${it.msgs.join("，")}`)
                                    .join("\n")}
                            </View>
                        </ScrollView>
                    );
                    await dialog.hide();
                } else if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.submitted) {
                    showQueryDialog("提示", error?.detail?.error?.message).then((index) => {
                        if (index == DialogIndex.positive) {
                            InventoryInAgent.addInventory({
                                ...req,
                                pharmacyNo: innerState.entryPharmacy?.no ?? 0,
                                pharmacyType: innerState.entryPharmacy?.type,
                                forceSubmit: 1,
                            })
                                .then(() => {
                                    if (this._draftId) this.draftManager.removeDraft(this._draftId);
                                    Toast.show("创建成功", { success: true }).then(() => {
                                        ABCNavigator.pop();
                                    });
                                })
                                .catch((error: any) => {
                                    showQueryDialog("提示", errorToStr(error));
                                });
                            ABCNavigator.pop(true);
                        } else {
                            if (this._draftId) this.draftManager.removeDraft(this._draftId);
                            ABCNavigator.pop();
                        }
                    });
                } else {
                    await dialog.fail("入库失败:" + errorSummary(error));
                }
                return;
            }

            await dialog.hide();
            //入库成功后，删除草稿
            if (rsp.status == InventoryInStatus.pass) {
                innerState.draft!.hasSubmit = true;
                const clinic = innerState.clinic ?? userCenter.clinic;

                await showImageConfirmDialog({
                    image: "image_dlg_success",
                    title: "入库成功",
                    content: `已更新${clinic!.displayName}的库存信息`,
                });
                if (this._draftId) await this.draftManager.removeDraft(this._draftId);
                ABCNavigator.pop(true);
            } else if (rsp.status == InventoryInStatus.waitAgree) {
                innerState.draft!.hasSubmit = true;
                await showImageConfirmDialog({
                    image: "add_stock_wait",
                    title: "等待审核",
                    content: "请在网页端查看审核信息",
                });
                if (this._draftId) await this.draftManager.removeDraft(this._draftId);
                ABCNavigator.pop(true); //true表示 本次创建了入库单
            } else {
                if (this._draftId) await this.draftManager.removeDraft(this._draftId);
                ABCNavigator.pop(true); //true表示 本次创建了入库单
            }
        }
    }

    private async updatePhotoAddDraft(result: PhotoRecognitionResultPageProps) {
        if (!!result) {
            // 处理每个识别的药品项
            result.inventoryMedicineItems?.forEach((item) => {
                // 这里调用添加药品到入库单的逻辑
                this.dispatch(new _EventAddToMedicineList(item));
            });
            // 处理附件
            if (result.imageUri) {
                const info = await OssImageUtils.uploadImage(
                    result.originalImagePath,
                    `${userCenter.clinic?.clinicId ?? ""}/${OssUpdateModules.GOODS}`
                );
                // 添加附件到草稿
                if (!this.innerState.draft?.extendData) {
                    const extendData = new ExtendData();
                    extendData.attachments = [];
                    this.innerState.draft!.extendData = extendData;
                }

                const item = JsonMapper.deserialize(AttachmentItem, {
                    fileName: FileUtils.getFileNameAndExt(new File(info.filePath!)),
                    url: info.url,
                    imageHeight: info.size?.height,
                    imageWidth: info.size?.width,
                });
                if (!this.innerState.draft?.extendData?.attachments?.length) {
                    this.innerState.draft.extendData.attachments = [];
                }
                this.innerState.draft?.extendData?.attachments?.push(item);
            }
            this._saveDraft();
            this.update();
        }
    }
    private async *_mapEventPhotoAdd(/*ignore: _EventPhotoAdd*/): AsyncGenerator<State> {
        const aiPicture = await InventoryInAgent.postOrderDraftAiPicture();
        console.log("this.isOnlineDraft>>>", this.isOnlineDraft);
        console.log("this.innerState.draft>>>", this.innerState.draft);

        if (!this.isOnlineDraft) {
            // a、没有药品（draft.medicines） ，一定调获取draftId的接口，然后赋值 this._draftId   innerState.draft.__draftId
            // b、 有药品draft.medicines有值
            // aa、 有拍照识别的数据（externalRelatedKey），说明此时已经调用过draftId的接口，这时不用再调获取draftId的接口
            // bb、 没有使用拍照识别数据（输入添加、扫码识别，没有这个值externalRelatedKey），这种情况调获取draftId的接口

            if (!this.innerState.draft?.medicines?.length || !this.innerState.draft?.medicines?.find((item) => item?.externalRelatedKey)) {
                // 没有药品
                this._draftId = aiPicture?.id;
                this.innerState.draft.__draftId = aiPicture?.id;
            }
        }
        console.log("this._draftId AAA>>>", this._draftId);

        await ABCNavigator.navigateToPage(
            <AbcImageRecoginition
                title="拍照添加商品"
                draftId={this._draftId}
                type={"0"}
                onResult={(result) => this.updatePhotoAddDraft(result)}
            />
        ),
            {
                replace: true,
            };
    }

    private async *_mapEventAddImage(): AsyncGenerator<State> {
        await AbcImagePicker.pickImageAndUpload(
            null,
            `${userCenter.clinic?.clinicId ?? ""}/${OssUpdateModules.GOODS}`,
            OssUpdateModules.GOODS
        ).then((result) => {
            if (result == null || _.isEmpty(result.url)) return;

            this.innerState.draft!.extendData!.attachments = this.innerState.draft?.extendData?.attachments ?? [];

            const item = JsonMapper.deserialize(AttachmentItem, {
                fileName: FileUtils.getFileNameAndExt(new File(result.filePath!)),
                url: result.url,
            });

            this.innerState.draft?.extendData?.attachments.push(item);
        });
        const innerState = this.innerState;

        yield innerState.clone();
    }

    private async *_mapEventRemoveImage(event: _EventRemoveImage): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.draft?.extendData?.attachments?.splice(event.index, 1);
        this._saveDraft();
        yield innerState.clone();
    }

    private async *_mapEventUpdateComment(event: _EventUpdateComment): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.draft!.comment = event.comment;
        this._saveDraft();
        yield innerState.clone();
    }

    private async *_mapEventOrderNo(event: _EventUpdateOrderNo): AsyncGenerator<State> {
        const innerState = this.innerState;
        innerState.draft!.outOrderNo = event.orderNo;
        this._saveDraft();
        yield innerState.clone();
    }
    private async *_mapEventModifyInspectUser(/*event: _EventModifyInspectUser*/): AsyncGenerator<State> {
        const draft = this.innerState.draft;
        const clinicEmployees = this.innerState.clinicEmployees;
        const selectedIndex = clinicEmployees?.findIndex((employee) => employee.employeeId == draft?.inspectUser?.id) ?? -1;

        const result = await showOptionsBottomSheet({
            title: "选择验收人",
            options: clinicEmployees?.map((employee) => employee.employeeName ?? "") ?? [],
            showConfirmBtn: true,
            initialSelectIndexes: selectedIndex !== -1 ? new Set<number>([selectedIndex]) : undefined,
        });
        if (!result) return;
        const currentEmployee = clinicEmployees[result[0]];
        draft.inspectUser = {
            id: currentEmployee.employeeId,
            name: currentEmployee.employeeName,
        };
        yield this.innerState.clone();
    }
    private async *_mapEventClear(/*ignore: _EventClear*/): AsyncGenerator<State> {
        const innerState = this.innerState;
        const select = await showQueryDialog("", "确认清空入库单？");
        if (select != DialogIndex.positive) return;
        this.innerState.draft!.medicines = [];
        this._saveDraft();
        yield innerState.clone();
    }

    private async _loadSupplier(): Promise<void> {
        const innerState = this.innerState;
        innerState.suppliers = await InventoryInAgent.getSupplierList({
            clinicId: innerState.clinic!.clinicId,
            pharmacyType: innerState.currentPharmacy?.no ?? 0,
        });
        innerState.suppliers = innerState.suppliers.filter((item) => item.status == SupplierItemStatus.enable);
    }

    private async *_mapEventDeleteDraft(): AsyncGenerator<State> {
        const result = await showQueryDialog("", "是否删除该入库单？");
        if (result == DialogIndex.positive) {
            this.draftManager.removeDraft(this.innerState.draft.__draftId!).then(() => {
                ABCNavigator.pop();
            });
        }
    }

    private async *_mapEventModifyClinicInfo(): AsyncGenerator<State> {
        if (!this.innerState.canModifyClinic) return;
        const innerState = this.innerState;
        const initIndex = innerState.clinicsList?.findIndex((item) => item.clinicId == innerState.clinic?.clinicId) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择门店",
            options: innerState.clinicsList?.map((item) => item.displayName ?? ""),
            initialSelectIndexes: new Set<number>([Math.max(initIndex, 0)]),
        });
        if (result && result.length) {
            const newClinic = innerState.clinicsList?.[result[0]];
            this.innerState.clinic = newClinic ?? this.innerState.clinic;
            this.innerState.draft.clinicId = this.innerState.clinic?.clinicId;
            this.innerState.draft.clinic = this.innerState.clinic;
            this.innerState.draft.toOrgan = JsonMapper.deserialize(InventoryInToOrgan, {
                id: newClinic?.clinicId, // 入库门店 ID
                name: newClinic?.name, // 入库门店名
                shortName: newClinic?.displayName,
            });
            this._saveDraft();
            this.update();
        }
    }

    private async *_mapEventBackPage(): AsyncGenerator<State> {
        /**
         * 返回时判断是否含有药品
         * 不含药品时要删除草稿
         */
        const innerState = this.innerState;
        if ((!this._isEditDraft || !this.isOnlineDraft) && innerState.hasChange) {
            const result = await showQueryDialog("是否保存", "是否需要保存成草稿");
            if (result == DialogIndex.negative) {
                this.draftManager.removeDraft(this.innerState.draft.__draftId!).then(() => {
                    ABCNavigator.pop();
                });
            } else if (result == DialogIndex.positive) {
                this.innerState.draft.toOrgan =
                    this.innerState.draft.toOrgan ??
                    JsonMapper.deserialize(InventoryInToOrgan, {
                        id: this.innerState.clinic?.clinicId, // 入库门店 ID
                        name: this.innerState.clinic?.name, // 入库门店名
                        shortName: this.innerState.clinic?.shortName,
                    });
                // 是否保存 toInventoryOnlineDraftReq
                console.log(
                    "是否保存",
                    "是否需要保存成草稿>>>> toInventoryOnlineDraftReq",
                    this.innerState.draft.toInventoryOnlineDraftReq(this.innerState.entryPharmacy)
                );
                InventoryInAgent.createStockInDraft(this.innerState.draft.toInventoryOnlineDraftReq(this.innerState.entryPharmacy))
                    .then(async (rsp) => {
                        //保存线上草稿成功,删除本地草稿
                        if (!!rsp.id) {
                            await this.draftManager.removeDraft(this.innerState.draft.__draftId!);
                        }
                        ABCNavigator.pop();
                    })
                    .catch((e) => {
                        showConfirmDialog("保存失败", errorSummary(e));
                    });
            }
        } else {
            if (innerState.hasChange) {
                const result = await showQueryDialog("", "草稿信息发生变动，是否保存？");
                if (result == DialogIndex.positive) {
                    // 草稿信息发生变动  toInventoryOnlineDraftReq
                    console.log(
                        " 草稿信息发生变动>>>> toInventoryOnlineDraftReq",
                        this.innerState.draft.toInventoryOnlineDraftReq(this.innerState.entryPharmacy)
                    );
                    InventoryInAgent.putStockInDraftDetail(
                        this._draftId!,
                        this.innerState.draft.toInventoryOnlineDraftReq(this.innerState.entryPharmacy)
                    )
                        .then(async (rsp) => {
                            //保存线上草稿成功,删除本地草稿
                            if (!!rsp.id) {
                                await this.draftManager.removeDraft(this.innerState.draft.__draftId!);
                            }
                            ABCNavigator.pop();
                        })
                        .catch((error) => {
                            if (error instanceof ABCApiError && error?.detail?.error?.code == InventoryApiErrorCode.saved) {
                                showQueryDialog("", error?.detail?.error?.message).then((operateResult) => {
                                    if (operateResult == DialogIndex.positive) {
                                        const req = this.innerState.draft.toInventoryOnlineDraftReq(this.innerState.entryPharmacy);
                                        req.forceSubmit = 1;
                                        InventoryInAgent.createStockInDraft(req)
                                            .then((rsp) => {
                                                //保存线上草稿成功,删除本地草稿
                                                if (rsp) {
                                                    this.draftManager.removeDraft(this.innerState.draft.__draftId!);
                                                }
                                            })
                                            .catch((e) => {
                                                showConfirmDialog("保存失败", errorSummary(e));
                                            });
                                    } else {
                                        this.draftManager.removeDraft(this.innerState.draft.__draftId!);
                                    }
                                    ABCNavigator.pop();
                                });
                                this.update();
                            } else {
                                showConfirmDialog("保存失败", errorSummary(error));
                            }
                        });
                } else {
                    this.draftManager.removeDraft(this._draftId!);
                    ABCNavigator.pop();
                }
            } else {
                ABCNavigator.pop();
            }
        }
    }

    private async *_mapEventSelectMultiplePharmacy(): AsyncGenerator<State> {
        const innerState = this.innerState;
        //拥有采购入库功能的药房列表
        const pharmacyList = innerState.pharmacyInfoConfig?.inventoryPharmacyList()?.filter((t) => t?.status == 1 && !!t?.enablePurchase);
        const initIndex = pharmacyList?.findIndex((item) => item.no == innerState.entryPharmacy?.no) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择库房",
            options: pharmacyList?.map((item) => item?.name ?? ""),
            initialSelectIndexes: new Set<number>([initIndex]),
        });
        if (_.isUndefined(result)) return;
        this.innerState.entryPharmacy = JsonMapper.deserialize(PharmacyListItem, pharmacyList?.[result[0]]);
        this.update();
    }
    private async *_mapEventSelectPurchasingMode(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selectedIndex = PurchaseType?.findIndex((item) => item.type == innerState.draft?.extendData?.purchaseType) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择采购方式",
            options: PurchaseType?.map((item) => item?.name ?? "") ?? [],
            showConfirmBtn: true,
            initialSelectIndexes: selectedIndex !== -1 ? new Set<number>([selectedIndex]) : undefined,
        });
        if (!result) return;
        this.innerState.draft.extendData = innerState.draft?.extendData ?? new ExtendData();
        this.innerState.draft.extendData.purchaseType = PurchaseType[result[0]].type;
        this._saveDraft();
        this.update();
    }
    private async *_mapEventSelectPurchasingPlatform(): AsyncGenerator<State> {
        const innerState = this.innerState;
        const selectedIndex = PlatFormType?.findIndex((item) => item.type == innerState.draft?.extendData?.platformType) ?? -1;
        const result = await showOptionsBottomSheet({
            title: "选择采购平台",
            options: PlatFormType?.map((item) => item?.name ?? "") ?? [],
            showConfirmBtn: true,
            initialSelectIndexes: selectedIndex !== -1 ? new Set<number>([selectedIndex]) : undefined,
        });
        if (!result) return;
        this.innerState.draft.extendData = innerState.draft?.extendData ?? new ExtendData();
        this.innerState.draft.extendData.platformType = PlatFormType[result[0]].type;
        this._saveDraft();
        this.update();
    }

    private async *_mapEventScrollToMedicine(event: _EventScrollToMedicine): AsyncGenerator<State> {
        yield ScrollToMedicineState.fromState(this.innerState, event.medicineIndex);
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state));
    }
}

class _Event extends BlocEvent {}

class _EventInit extends _Event {}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventClear extends _Event {}

class _EventUpdateOrderNo extends _Event {
    orderNo?: string;

    constructor(orderNo?: string) {
        super();
        this.orderNo = orderNo;
    }
}

class _EventModifyInspectUser extends _Event {}

class _EventUpdateComment extends _Event {
    comment?: string;

    constructor(comment?: string) {
        super();
        this.comment = comment;
    }
}

class _EventSubmit extends _Event {}

class _EventPhotoAdd extends _Event {}

class _EventAddImage extends _Event {}

class _EventRemoveImage extends _Event {
    index: number;

    constructor(index: number) {
        super();
        this.index = index;
    }
}
class _EventOpenItemTap extends _Event {
    constructor(item: InventoryMedicineItem, createType?: InventoryInType) {
        super();
        this.item = item;
        this.createType = createType;
    }

    item: InventoryMedicineItem;
    createType?: InventoryInType;
}

class _EventScanToInventory extends _Event {
    isFromScanEnter?: boolean;
    constructor(isFromScanEnter?: boolean) {
        super();
        this.isFromScanEnter = isFromScanEnter;
    }
}

class _EventSelectSupplier extends _Event {}

class _EventInputAdd extends _Event {}

class _EventAddToMedicineList extends _Event {
    item: InventoryMedicineItem;
    originalItem?: InventoryMedicineItem; // 添加原始药品项字段

    constructor(item: InventoryMedicineItem, originalItem?: InventoryMedicineItem) {
        super();
        this.item = item;
        this.originalItem = originalItem;
    }
}

class _EventDeleteMedicine extends _Event {
    item: InventoryMedicineItem;

    constructor(item: InventoryMedicineItem) {
        super();
        this.item = item;
    }
}

class _EventDeleteDraft extends _Event {}

class _EventModifyClinicInfo extends _Event {}

class _EventBackPage extends _Event {}

class _EventSelectMultiplePharmacy extends _Event {}

class _EventScrollToMedicine extends _Event {
    medicineIndex: number;

    constructor(medicineIndex: number) {
        super();
        this.medicineIndex = medicineIndex;
    }
}
class _EventSelectPurchasingMode extends _Event {}
class _EventSelectPurchasingPlatform extends _Event {}
