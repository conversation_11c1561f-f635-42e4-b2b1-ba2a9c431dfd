/**
 * 成都字节星期科技公司
 * <AUTHOR>
 * @date 2020-03-20
 *
 * @description
 */

import { Bloc, BlocEvent } from "../../bloc";
import React from "react";
import { actionEvent, EventName } from "../../bloc/bloc";
import { ChargeForm, ChargeFormItem, ChargeSourceFormType, ChargeInvoiceType, ChargeFormItemStatus } from "../../charge/data/charge-beans";
import {
    AntibioticEnum,
    ChineseMedicineSpecType,
    GoodsInfo,
    GoodsType,
    GoodsTypeId,
    Patient,
    UsageInfo,
} from "../../base-business/data/beans";
import { MedicineAddGroup, MedicineUsageInput } from "../../outpatient/medicine-add-page/medicine-add-page";
import { ABCNavigator } from "../../base-ui/views/abc-navigator";
import { ProductAddPage } from "../../outpatient/product-add-page/product-add-page";
import { ProductMedicineUsageParams } from "../../outpatient/product-add-page/product-add-page-bean";
import { JsonMapper } from "../../common-base-module/json-mapper/json-mapper";
import { MedicineAddType } from "../../outpatient/data/outpatient-const";
import { ChargeUtils } from "../../charge/utils/charge-utils";
import { errorSummary, UUIDGen, errorToStr } from "../../common-base-module/utils";
import { ExecuteInvoiceDraftManager } from "../data/execute-invoice-draft-manager";
import { SingleBargainDialog } from "../../charge/single-bargain-dialog";
import { Subject, of } from "rxjs";
import { debounceTime, switchMap } from "rxjs/operators";
import { AnyType } from "../../common-base-module/common-types";
import { ABCError } from "../../common-base-module/common-error";
import _, { isUndefined } from "lodash";
import { LoadingDialog } from "../../base-ui/dialog/loading-dialog";
import { PatientOrderDataAgent } from "../data/nurse-station-data";
import ExecuteInvoicePage from "../execute-invoice-page";
import { Toast } from "../../base-ui/dialog/toast";
import { CrmAgent } from "../../patients/data/crm-agent";
import { GoodsUtils } from "../../base-business/utils/utils";
import { StringUtils } from "../../base-ui/utils/string-utils";
import { ShebaoAgent } from "../../base-business/mix-agent/shebao-agent";
import { userCenter } from "../../user-center";
import { ProductAddExecutorDialog } from "../../outpatient/product-add-page/views/product-add-executor-dialog";
import { AbcMap } from "../../base-ui/utils/abc-map";
import { DirectChargeMedicineAddInfo, DirectChargeMedicineInvokeParams } from "../../charge/direct-charge-medicine-add-page";
import { ChargeItemModifyUtils } from "../../charge/utils/charge-item-modify-utils";
import { InventoryClinicConfig } from "../../inventory/data/inventory-bean";
import { AbcDialog } from "../../base-ui/abc-app-library";
import { pxToDp } from "../../base-ui/utils/ui-utils";
import { MedicineUsagePicker } from "../../base-ui/picker/medicine-usage-picker";
import { NurseStationConfig, OnlinePropertyConfigProvider } from "../../data/online-property-config-provder";
import { DialogIndex, showConfirmDialog, showQueryDialog } from "../../base-ui/dialog/dialog-builder";
import { ChargeExecuteStatus } from "../data/bean";
import URLProtocols from "../../url-dispatcher/url-protocols";
import { RegistrationFormItem, ChargeInvoiceDetailData } from "../../registration/data/bean";
import { ExecuteRegistrationDetailDialog } from "./execute-registration-detail-dialog";
import { showBottomPanel } from "../../base-ui/abc-app-library/panel";
import { ChargeAddMedicinePage } from "../../charge/view/charge-add-medicine-page";
import { AntimicrobialDrugConfig, ClinicEmployeesInfo, OutpatientAgent } from "../../outpatient/data/outpatient";
import { delayed } from "../../common-base-module/rxjs-ext/rxjs-ext";
import { IconFontView } from "../../base-ui";
import { Colors, Sizes } from "../../theme";
import { AcupunctureItem } from "../../outpatient/acupuncture/data/acupuncture-bean";
import { AcuPointsItem } from "../../outpatient/data/outpatient-beans";
import { AbcSet } from "../../base-ui/utils/abc-set";
import { AcupunctureDialog } from "../../outpatient/acupuncture/acupuncture-page";
import abcI18Next from "../../language/config";
import { ClinicAgent, EmployeesMeConfig } from "../../base-business/data/clinic-agent";

class _Event extends BlocEvent {}

class _EventInit extends _Event {
    draftId?: string;
    patient?: Patient;
    registrationId?: string;
    chargeSheetId?: string;
    registrationDetail?: RegistrationFormItem; //预约详情
    constructor(
        draftId?: string,
        patient?: Patient,
        registrationId?: string,
        chargeSheetId?: string,
        registrationDetail?: RegistrationFormItem
    ) {
        super();
        this.draftId = draftId;
        this.patient = patient;
        this.registrationId = registrationId;
        this.chargeSheetId = chargeSheetId;
        this.registrationDetail = registrationDetail;
    }
}

class _EventUpdate extends _Event {
    state?: State;

    constructor(state?: State) {
        super();
        this.state = state;
    }
}

class _EventUpdatePatient extends _Event {
    patient: Patient;
    constructor(patient: Patient) {
        super();
        this.patient = patient;
    }
}

class _EventAddTreatmentItems extends _Event {
    chargeFormItem?: ChargeFormItem;
    constructor(chargeFormItem?: ChargeFormItem) {
        super();
        this.chargeFormItem = chargeFormItem;
    }
}

class _EventUpdateDoctor extends _Event {
    id: string;
    name: string;
    departmentId?: string;
    departmentName?: string;
    constructor(id: string, name: string, departmentId?: string, departmentName?: string) {
        super();
        this.id = id;
        this.name = name;
        this.departmentId = departmentId;
        this.departmentName = departmentName;
    }
}

class _EventUpdateDiagnosis extends _Event {
    diagnosis: string;
    constructor(diagnosis: string) {
        super();
        this.diagnosis = diagnosis;
    }
}
class _EventBack extends _Event {}

class _EventDelete extends _Event {}

class _EventPreviewPrice extends _Event {}

class _EventSubmit extends _Event {}

class _EventModifyProductEmployee extends _Event {}

class _EventDeleteChargeFormItem extends _Event {
    formItem: ChargeFormItem;
    constructor(formItem: ChargeFormItem) {
        super();
        this.formItem = formItem;
    }
}

class _EventChangeChargeFormItem extends _Event {
    formItem: ChargeFormItem;
    count?: number;
    unit?: string;
    constructor(formItem: ChargeFormItem, count?: number, unit?: string) {
        super();
        this.formItem = formItem;
        this.count = count;
        this.unit = unit;
    }
}

class _EventChangeMedicinePharmacy extends _Event {
    form: ChargeForm;
    formItem: ChargeFormItem;
    isDelete: boolean;
    constructor(form: ChargeForm, formItem: ChargeFormItem, isDelete: boolean) {
        super();
        this.form = form;
        this.formItem = formItem;
        this.isDelete = isDelete;
    }
}

class _EventChangeChineseMedicinePharmacy extends _Event {
    form: ChargeForm;
    constructor(form: ChargeForm) {
        super();
        this.form = form;
    }
}

class _EventModifyChargeItemRemark extends _Event {
    formItem: ChargeFormItem;
    isDelete: boolean;
    showAcuPointsIcon?: boolean;
    constructor(formItem: ChargeFormItem, isDelete: boolean, showAcuPointsIcon?: boolean) {
        super();
        this.formItem = formItem;
        this.isDelete = isDelete;
        this.showAcuPointsIcon = showAcuPointsIcon;
    }
}

class _EventSaveEditDetail extends _Event {}

class _EventShowRegistrationDetail {}

class _EventChangeDosageCount extends _Event {
    chargeForm: ChargeForm;
    dosageCount: number;

    constructor(chargeForm: ChargeForm, dosageCount: number) {
        super();
        this.chargeForm = chargeForm;
        this.dosageCount = dosageCount;
    }
}

class _EventChangeUsage extends _Event {
    chargeForm: ChargeForm;
    usageInfo: UsageInfo;

    constructor(chargeForm: ChargeForm, usageInfo: UsageInfo) {
        super();
        this.chargeForm = chargeForm;
        this.usageInfo = usageInfo;
    }
}

class _EventContinueOperate extends _Event {}

export class State {
    loading = true;
    detailData!: ChargeInvoiceDetailData;
    newLocalDraft = false;
    hasChanged = false;
    needContinueAddMedicine = true;

    calculating = false;
    calculateError: AnyType;
    showErrorHint = false;

    //是否可以更换患者
    patientSwitchable = true;

    //多药房相关配置
    pharmacyInfoConfig?: InventoryClinicConfig;

    //执行站配置
    nurseStationConfig?: NurseStationConfig;

    antimicrobialDrugConfig?: AntimicrobialDrugConfig; // 抗菌配置
    //当前处方医生的基础信息
    currentDoctorDetailInfo?: ClinicEmployeesInfo;
    /**
     * 是否开启医生职称限制
     */
    get enableDoctorPractice(): boolean {
        return !!this.antimicrobialDrugConfig?.isOpenAntimicrobialDrug;
    }
    //允许的抗菌药物
    get allowAntibioticList(): AntibioticEnum[] | undefined {
        return this.antimicrobialDrugConfig?.checkDoctorUseAntimicrobialTypes(this.currentDoctorDetailInfo?.chainInfo?.practiceInfo);
    }
    //开药时是否校验药物类型
    get allowAntibiotic(): boolean {
        return this.allowAntibioticList != undefined;
    }
    get projectAmountStatisticsStr(): string {
        const chargeForms = this.detailData?.chargeForms;
        let medicinesAmount = 0,
            medicineCount = 0;
        chargeForms?.forEach((form) => {
            medicineCount += form.chargeFormItems?.length ?? 0;
            medicinesAmount += form.totalPrice ?? 0;
        });
        return `${medicineCount}项，${abcI18Next.t("¥")}${medicinesAmount.toFixed(2)}`;
    }

    diagnoseCount?: number; //就诊历史数量

    employeesMeConfig?: EmployeesMeConfig;

    //执行站能查看就诊历史
    get canViewDiagnoseHistory(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.nurse?.medicalHistory;
    }

    //执行站能查看手机号
    get canSeePatientMobileInExecution(): boolean {
        return !!this.employeesMeConfig?.employeeDataPermission?.nurse?.isCanSeePatientMobile;
    }

    //执行站可开项目
    get canOpenItemGoodsTypes(): Array<{ type: number }> {
        let goodsType = [{ type: 3 }, { type: 4 }, { type: 19 }, { type: 11 }];
        if (this.isOphthalmologyClinic) {
            goodsType.push({ type: 24 });
        }
        //药品
        if (this.nurseStationConfig?.canOpenGoodsTypes?.includes(1)) {
            goodsType.push({ type: 1 });
        }
        //商品、耗材
        if (this.nurseStationConfig?.canOpenGoodsTypes?.includes(2)) {
            goodsType.push({ type: 7 });
            goodsType.push({ type: 2 });
        }
        // 医院--执行开单搜索不传type:19
        if (userCenter.clinic?.isNormalClinic) {
            goodsType = goodsType.filter((t) => t.type != 19);
        }
        return goodsType;
    }

    /**
     * 是否是编辑草稿
     */
    get isEditDraft(): boolean {
        return this.newLocalDraft;
    }

    /**
     * 口腔诊所
     */
    get isDentistry(): boolean {
        return !!userCenter.clinic?.isDentistryClinic;
    }

    /**
     * 眼科诊所
     */
    get isOphthalmologyClinic(): boolean {
        return !!userCenter.clinic?.isOphthalmologyClinic;
    }

    /**
     * 当前添加的项目
     */
    get treatmentChargeForm(): ChargeFormItem[] {
        return [
            ...(this.detailData.getChargeForm(ChargeSourceFormType.treatment)?.chargeFormItems ?? []),
            ...(this.detailData.getChargeForm(ChargeSourceFormType.package)?.chargeFormItems ?? []),
        ];
    }

    get doctorDisplayStr(): string {
        const str: Set<string> = new Set<string>();
        for (const item of this.treatmentChargeForm) {
            !!item.doctorName && str.add(item.doctorName);
        }
        return [...str.values()].join("、");
    }

    get nurseDisplayStr(): string {
        const str: Set<string> = new Set<string>();
        for (const item of this.treatmentChargeForm) {
            !!item.nurseName && str.add(item.nurseName);
        }
        return [...str.values()].join("、");
    }

    // 是否有执行记录
    get hasExecuteRecord(): boolean {
        if (this.detailData == null) return false;

        for (const form of this.detailData.chargeForms ?? []) {
            for (const formItem of form.chargeFormItems ?? []) {
                if ((formItem.executedUnitCount ?? 0) > 0) return true;

                if (formItem.composeChildren == null) continue;

                for (const subFormItem of formItem.composeChildren) {
                    if ((subFormItem.executedUnitCount ?? 0) > 0) return true;
                }
            }
        }
        return false;
    }

    /**
     * 执行单是否可被修改
     */
    get executeSheetCanModified(): boolean {
        if (this.detailData) {
            if (
                this.detailData?.type == ChargeInvoiceType.therapy &&
                (this.detailData?.status as Number) == ChargeFormItemStatus.unCharged &&
                (this.detailData?.executeStatus ?? 0) <= ChargeExecuteStatus.EXECUTION_ORDER_STATUS_UNEXECUTED
            ) {
                return true;
            }
            if (this.detailData.isLocalDraft) return true;
        }
        return false;
    }

    /**
     * 执行单是否可被删除
     */
    get executeSheetCanDelete(): boolean {
        return this.executeSheetCanModified && !this.hasExecuteRecord;
    }

    currentFocusItem?: GoodsInfo | null = null; // 当前添加的项目
    groups?: GoodsInfo[] | null = null; // 所有已添加的开单项目组

    isSelect(group: AnyType, medicine: AnyType): boolean {
        const _selectItem =
            this.currentFocusItem &&
            medicine &&
            (medicine.id != undefined && medicine.id != ""
                ? medicine.id == this.currentFocusItem.id
                : medicine.displayName == this.currentFocusItem.displayName);
        return this.currentFocusItem == medicine || _selectItem;
    }

    clone(): State {
        return Object.assign(new State(), this);
    }
}

export class ScrollToFocusItemState extends State {
    static fromState(state: State): ScrollToFocusItemState {
        const newState = new ScrollToFocusItemState();
        Object.assign(newState, state);
        return newState;
    }
}

export class ExecuteInvoiceCreatePageBloc extends Bloc<_Event, State> {
    static Context = React.createContext<ExecuteInvoiceCreatePageBloc | undefined>(undefined);

    static fromContext(context: ExecuteInvoiceCreatePageBloc): ExecuteInvoiceCreatePageBloc {
        return context;
    }

    private _innerState!: State;

    private _calculateTrigger = new Subject<number>();
    private _getHistoryListTrigger = new Subject<string>(); // 就诊历史

    constructor(options: {
        draftId?: string;
        patient?: Patient;
        registrationId?: string;
        chargeSheetId?: string;
        registrationDetail?: RegistrationFormItem;
    }) {
        super();
        const { draftId, patient, registrationId, chargeSheetId, registrationDetail } = options;
        this.dispatch(new _EventInit(draftId, patient, registrationId, chargeSheetId, registrationDetail)).then();
    }

    get innerState(): State {
        if (!this._innerState) this._innerState = new State();

        return this._innerState;
    }

    initialState(): State {
        return this.innerState;
    }

    createEventHandlers(): Map<EventName, Function> {
        const map = new Map<EventName, Function>();
        map.set(_EventInit, this._mapEventInit);
        map.set(_EventUpdate, this._mapEventUpdate);
        map.set(_EventUpdatePatient, this._mapEventUpdatePatient);
        map.set(_EventAddTreatmentItems, this._mapEventNewAddTreatmentItems);
        map.set(_EventUpdateDoctor, this._mapEventUpdateDoctor);
        map.set(_EventUpdateDiagnosis, this._mapEventUpdateDiagnosis);
        map.set(_EventBack, this._mapEventBack);
        map.set(_EventDelete, this._mapEventDelete);
        map.set(_EventPreviewPrice, this._mapEventPreviewPrice);
        map.set(_EventSubmit, this._mapEventSubmit);

        return map;
    }

    public update(state?: State): void {
        this.dispatch(new _EventUpdate(state)).then();
    }

    public requestUpdatePatient(patient: Patient): void {
        this.dispatch(new _EventUpdatePatient(patient)).then();
    }

    public requestAddTreatmentItems(chargeFormItem?: ChargeFormItem): void {
        this.dispatch(new _EventAddTreatmentItems(chargeFormItem)).then();
    }

    public requestUpdateDoctor(id: string, name: string, departmentId?: string, departmentName?: string): void {
        this.dispatch(new _EventUpdateDoctor(id, name, departmentId, departmentName));
    }

    public requestUpdateDiagnosis(diagnosis: string): void {
        this.dispatch(new _EventUpdateDiagnosis(diagnosis));
    }

    public requestBack(): void {
        this.dispatch(new _EventBack());
    }

    public requestDelete(): void {
        this.dispatch(new _EventDelete());
    }

    public requestPreviewPrice(): void {
        this.dispatch(new _EventPreviewPrice());
    }

    public requestSubmit(): void {
        this.dispatch(new _EventSubmit());
    }

    public requestModifyProductEmployee(): void {
        this.dispatch(new _EventModifyProductEmployee());
    }

    public requestDeleteChargeFormItem(chargeItem: ChargeFormItem): void {
        this.dispatch(new _EventDeleteChargeFormItem(chargeItem));
    }

    public requestChangeMedicineCount(formItem: ChargeFormItem, count?: number, unit?: string): void {
        this.dispatch(new _EventChangeChargeFormItem(formItem, count, unit));
    }

    public requestChangeMedicinePharmacy(form: ChargeForm, formItem: ChargeFormItem, isDelete: boolean): void {
        this.dispatch(new _EventChangeMedicinePharmacy(form, formItem, isDelete));
    }

    public requestChangeChineseMedicinePharmacy(form: ChargeForm): void {
        this.dispatch(new _EventChangeChineseMedicinePharmacy(form));
    }

    public requestModifyChargeItemRemark(chargeFormItem: ChargeFormItem, isDelete: boolean, showAcuPointsIcon?: boolean): void {
        this.dispatch(new _EventModifyChargeItemRemark(chargeFormItem, isDelete, showAcuPointsIcon));
    }

    /**
     * 保存修改开单项目内容
     */
    public requestSaveEditDetail(): void {
        this.dispatch(new _EventSaveEditDetail());
    }

    public requestShowRegistrationDetail(): void {
        this.dispatch(new _EventShowRegistrationDetail());
    }

    requestChangeDosageCount(chargeForm: ChargeForm, dosageCount: number): void {
        this.dispatch(new _EventChangeDosageCount(chargeForm, dosageCount));
    }

    public requestChangeUsage(chargeForm: ChargeForm, usageInfo: UsageInfo): void {
        this.dispatch(new _EventChangeUsage(chargeForm, usageInfo));
    }

    requestContinueOperate(): void {
        this.dispatch(new _EventContinueOperate());
    }
    private async _initPageConfig(options?: {
        draftId?: string;
        registrationId?: string;
        chargeSheetId?: string;
        patient?: Patient;
        registrationDetail?: RegistrationFormItem;
    }): Promise<void> {
        Promise.all([
            ClinicAgent.getEmployeesMeConfig().catchIgnore(),
            userCenter.getInventoryChainConfig(false).catchIgnore(),
            OnlinePropertyConfigProvider.instance.getNurseStationConfig().catchIgnore(),
            options?.patient ? CrmAgent.getPatientById(options?.patient?.id).catchIgnore() : undefined, //刷新患者信息
            OutpatientAgent.getAntimicrobialDrugConfig().catchIgnore(),
            GoodsUtils.initGoodsCustomUnitIfNeed().catchIgnore(),
        ]).then((rsp) => {
            const [employeesMeConfig, pharmacyInfoConfig, nurseStationConfig, patientInfo, antimicrobialDrugConfig] = rsp;
            this.innerState.employeesMeConfig = employeesMeConfig;
            this.innerState.pharmacyInfoConfig = pharmacyInfoConfig;
            this.innerState.nurseStationConfig = nurseStationConfig;
            if (options?.patient) this.innerState.detailData.patient = patientInfo;
            this.innerState.antimicrobialDrugConfig = antimicrobialDrugConfig;
        });
        let draftId = options?.draftId;
        if (options?.registrationId && !draftId) {
            const allDraft = await ExecuteInvoiceDraftManager.instance.getAllDrafts().catchIgnore();
            draftId = allDraft?.find((item) => item.registrationId__ === options.registrationId)?.localDraftId;
        }

        if (draftId != undefined) {
            // 读取执行单草稿
            this.innerState.detailData = await ExecuteInvoiceDraftManager.instance.loadDraftChargeDetailData(draftId).catch(() => {
                const data = ExecuteInvoiceDraftManager.instance.createDraft();
                data.localDraftId = draftId;
                return data;
            });
            this.innerState.loading = false;
            this.innerState.newLocalDraft = true;
        } else {
            if (!!options?.chargeSheetId) {
                // 修改执行单
                this.innerState.loading = true;
                const detailData = await PatientOrderDataAgent.getPatientOrderDetailInfo(options.chargeSheetId).then();

                /**
                 * 收费内容全部转换为本地添加
                 */
                detailData?.chargeForms?.map((form) => {
                    if (
                        !form.chargeFormItems?.length ||
                        form.isDecoction ||
                        form.isDelivery ||
                        form.isAirPharmacy ||
                        form.isVirtualPharmacy
                    )
                        return;
                    form.chargeFormItems.map((formItem) => {
                        formItem.localAdd = true;
                    });
                });
                this.innerState.detailData = detailData;
                this.innerState.newLocalDraft = false;
                this.innerState.loading = false;
            } else {
                // 新建执行单草稿
                this.innerState.detailData = ExecuteInvoiceDraftManager.instance.createDraft();
                this.innerState.newLocalDraft = true;
                this.innerState.loading = false;
            }
        }

        if (options?.registrationId) this.innerState.detailData.registrationId__ = options.registrationId;
        if (options?.registrationDetail) this.innerState.detailData.registrationDetail__ = options?.registrationDetail;

        if (options?.patient) {
            this.innerState.patientSwitchable = false;
            this.innerState.detailData.patient = options.patient;
        }
    }
    private _initPageTrigger(): void {
        this._calculateTrigger
            .pipe(
                debounceTime(this.innerState.calculating ? 200 : 0),
                switchMap(() => {
                    this.innerState.calculating = true;
                    this.innerState.calculateError = undefined;
                    this.update();

                    return ChargeUtils.calculatingPrice(this.innerState.detailData).catch((error) => new ABCError(error));
                })
            )
            .subscribe((rsp) => {
                this.innerState.calculating = false;
                if (rsp instanceof ABCError) {
                    this.innerState.calculateError = rsp.detailError;
                } else {
                    ChargeUtils.syncChargeInvoiceDetail(this.innerState.detailData, rsp);
                }

                this.update();
            })
            .addToDisposableBag(this);

        //就诊历史
        this._getHistoryListTrigger
            .pipe(
                switchMap((/*data*/) => {
                    const patientId = this.innerState.detailData.patient?.id;
                    if (!patientId) return of(null);

                    return OutpatientAgent.getOutpatientHistoryList(patientId);
                })
            )
            .subscribe(
                (patientSummaryList) => {
                    if (!patientSummaryList) return;
                    this.innerState.diagnoseCount = patientSummaryList.totalCount;
                    this.update();
                },
                (error) => {
                    this.innerState.loading = false;
                    new ABCError(error);
                    this.update();
                }
            )
            .addToDisposableBag(this);
    }
    private async *_mapEventInit(event: _EventInit): AsyncGenerator<State> {
        await this._initPageConfig({
            draftId: event.draftId,
            registrationId: event.registrationId,
            chargeSheetId: event.chargeSheetId,
            patient: event.patient,
            registrationDetail: event.registrationDetail,
        });
        this._initPageTrigger();
        this._calculateTrigger.next(0);
        if (!!this.innerState.detailData.patient?.id) this._getHistoryListTrigger.next();

        yield this.innerState.clone();
    }

    private async *_mapEventUpdate(event: _EventUpdate): AsyncGenerator<State> {
        if (event.state) {
            yield event.state;
            return;
        }
        yield this.innerState.clone();
    }

    private async *_mapEventUpdatePatient(event: _EventUpdatePatient): AsyncGenerator<State> {
        this.innerState.detailData.patient = event.patient;
        this.innerState.hasChanged = true;
        this._getHistoryListTrigger.next();
        yield this.innerState.clone();
    }

    @actionEvent(_EventContinueOperate)
    async *_mapEventChangeFocusItem(): AsyncGenerator<State> {
        if (!this.innerState.hasChanged) return;
        if (!this.innerState.needContinueAddMedicine) return;
        this.innerState.currentFocusItem = null;
        this.innerState.hasChanged = false;
        delayed(500).subscribe(() => {
            this.requestAddTreatmentItems();
        });
    }

    private async *_mapEventAddTreatmentItems(event: _EventAddTreatmentItems): AsyncGenerator<State> {
        const detailData = this.innerState.detailData;
        const treatmentChargeForm = detailData.getChargeForm(ChargeSourceFormType.treatment);
        const packageChargeForm = detailData.getChargeForm(ChargeSourceFormType.package);
        const nurseFeeChargeForm = detailData.getChargeForm(ChargeSourceFormType.nurseProductFee);
        const chargeFormItems = [
            ...(treatmentChargeForm?.chargeFormItems ?? []),
            ...(packageChargeForm?.chargeFormItems ?? []),
            ...(nurseFeeChargeForm?.chargeFormItems ?? []),
        ];
        let group = MedicineAddGroup.fromFormItems(ChargeUtils.chargeFormItemsToPrescriptionFormItems(chargeFormItems));
        const input: MedicineUsageInput = await ABCNavigator.navigateToPage(
            <ProductAddPage
                medicineUsageParams={JsonMapper.deserialize(ProductMedicineUsageParams, {
                    title: "诊疗项目",
                    type: MedicineAddType.treatment,
                    patient: this.innerState.detailData?.patient,
                    medicines: group != null ? [group] : undefined,
                    selectMedicine: event.chargeFormItem?.goodsInfo,
                    selectGroup: group,
                    switchTypes: [MedicineAddType.treatment, MedicineAddType.package],
                })}
            />
        );
        this.innerState.needContinueAddMedicine = !!input;
        if (input == undefined) return;

        //删除可编辑项（未收费项）
        group = input.medicines![0];
        const treatment: GoodsInfo[] = [];
        const packages: GoodsInfo[] = [];
        const nurseProduct: GoodsInfo[] = [];
        group?.selectedMedicines?.forEach((item: GoodsInfo) => {
            if (item.type == GoodsType.treatment) treatment.push(item);
            else if (item.type == GoodsType.package) packages.push(item);
            else if (item.type == GoodsType.nurseProduct) nurseProduct.push(item);
        });

        function createChargeForm(goods: GoodsInfo[], sourceFormType: ChargeSourceFormType) {
            const newFormItems = goods.map((item: GoodsInfo) => {
                const usage = group?.inputUsages.get(item);
                return ChargeUtils.createChargeFormItem(item, {
                    count: usage?.unitCount,
                    unit: usage?.unit,
                    doctorId: usage?.doctorId,
                    doctorName: usage?.doctorName,
                    departmentId: usage?.departmentId,
                    departmentName: usage?.departmentName,
                    nurseId: usage?.nurseId,
                    nurseName: usage?.nurseName,
                });
            });

            let chargeForm = detailData.getChargeForm(sourceFormType);
            if (!chargeForm) {
                chargeForm = new ChargeForm();
                chargeForm.sourceFormType = sourceFormType;
                chargeForm.keyId = UUIDGen.generate();
                detailData.chargeForms = detailData.chargeForms ?? [];
                detailData.chargeForms!.push(chargeForm);
            }

            chargeForm.chargeFormItems = newFormItems;
        }

        createChargeForm(treatment, ChargeSourceFormType.treatment);
        createChargeForm(packages, ChargeSourceFormType.package);
        createChargeForm(nurseProduct, ChargeSourceFormType.nurseProductFee);

        this.innerState.hasChanged = true;
        this._calculateTrigger.next(0);
        yield this.innerState.clone();
    }

    @actionEvent(_EventDeleteChargeFormItem)
    private async *_mapEventDeleteChargeFormItem(event: _EventDeleteChargeFormItem): AsyncGenerator<State> {
        const _innerState = this.innerState;
        const chargeFormItem = event.formItem;

        if (!(await ChargeItemModifyUtils.checkDeleteChargeItems([chargeFormItem]))) return;

        const chargeForm = _innerState.detailData?.chargeForms?.find((form) => (form.chargeFormItems?.indexOf(event.formItem!) ?? 0) >= 0);
        _.remove(chargeForm!.chargeFormItems!, event.formItem);
        //如果某个form里收费项全部删除了，则同时删除该form项
        if (_.isEmpty(chargeForm?.chargeFormItems)) {
            _.remove(_innerState.detailData!.chargeForms!, chargeForm);
        }

        this.innerState.detailData!.markPatientPointsInfoChecked(false);
        _innerState.hasChanged = true;
        this.update();
    }

    @actionEvent(_EventChangeChargeFormItem)
    private async *_mapEventChangeChargeFormItem(event: _EventChangeChargeFormItem): AsyncGenerator<State> {
        const { formItem, count, unit } = event;
        if (count != null) {
            formItem.unitCount = count;
            if (formItem.expectedTotalPrice != null) {
                formItem.expectedTotalPrice = undefined;
                formItem.expectedUnitPrice = formItem.unitPrice;
            }
        }

        if (unit != null) {
            formItem.setUnit(unit);
        }

        this.innerState.detailData!.markPatientPointsInfoChecked(false);
        this.innerState.hasChanged = true;
        this._calculateTrigger.next(0);
        this.update();
    }

    @actionEvent(_EventChangeMedicinePharmacy)
    private async *_mapEventChangeMedicinePharmacy(event: _EventChangeMedicinePharmacy): AsyncGenerator<State> {
        const { formItem, isDelete } = event;
        const { pharmacyInfoConfig } = this.innerState;
        let stockInfo;
        const defaultPharmacyInfo = pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: this.innerState.detailData?.departmentId,
            goodsInfo: {
                typeId: formItem?.goodsInfo?.typeId,
                customTypeId: formItem?.goodsInfo?.customTypeId,
            },
        });
        if (isDelete) {
            formItem.pharmacyNo = defaultPharmacyInfo?.no;
            formItem.pharmacyName = defaultPharmacyInfo?.name;
            formItem.pharmacyType = defaultPharmacyInfo?.type;
            stockInfo = formItem.goodsInfo.pharmacyGoodsStockList?.find((item) => item.pharmacyNo == formItem.pharmacyNo);
        } else {
            if (!pharmacyInfoConfig?.filterNormalPharmacyList?.length) return;

            const pharmacyNo: number | undefined = formItem.pharmacyNo ?? formItem.goodsInfo.pharmacyNo;

            const filterExistPharmacy = pharmacyInfoConfig?.filterNormalPharmacyList?.find((pharmacy) => pharmacy.no == pharmacyNo);
            const selectPharmacyInfo = !!filterExistPharmacy ? filterExistPharmacy : defaultPharmacyInfo;
            const initIndex = pharmacyInfoConfig?.filterNormalPharmacyList?.findIndex((t) => t.no == selectPharmacyInfo?.no);

            const result = await AbcDialog.showOptionsBottomSheet({
                title: "药品来源",
                options: pharmacyInfoConfig?.filterNormalPharmacyList.map(
                    (item) => `${item.name}${defaultPharmacyInfo?.no == item.no ? "(默认)" : ""}`
                ),
                initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
                crossAxisCount: 2,
            });
            if (_.isUndefined(result)) return;
            if (!!result) {
                const selected = pharmacyInfoConfig?.filterNormalPharmacyList[result[0]];
                stockInfo = formItem.goodsInfo.pharmacyGoodsStockList?.find((item) => item.pharmacyNo == selected.no);
                formItem.pharmacyNo = selected.no;
                formItem.pharmacyName = selected.name;
                formItem.pharmacyType = selected.type;
            }
        }
        const { stockPackageCount = 0, stockPieceCount = 0 } = stockInfo ?? {};
        formItem.stockPackageCount = stockPackageCount;
        formItem.stockPieceCount = stockPieceCount;
        formItem.productInfo = Object.assign(formItem.productInfo ?? {}, {
            stockPackageCount,
            stockPieceCount,
        });
        this.update();
    }

    @actionEvent(_EventChangeChineseMedicinePharmacy)
    private async *_mapEventChangeChineseMedicinePharmacy(event: _EventChangeChineseMedicinePharmacy): AsyncGenerator<State> {
        const { pharmacyInfoConfig } = this.innerState;
        if (!pharmacyInfoConfig?.filterNormalPharmacyList?.length) return;
        const initGoodsTypeId =
            event.form?.specification == ChineseMedicineSpecType.fullNames()[1]
                ? GoodsTypeId.medicineChineseGranule
                : GoodsTypeId.medicineChinesePiece;
        const pharmacyNo = event.form?.pharmacyNo;
        const defaultPharmacyInfo = pharmacyInfoConfig?.getDefaultPharmacy({
            departmentId: this.innerState.detailData?.departmentId,
            goodsInfo: {
                typeId: initGoodsTypeId,
            },
        });

        const filterExistPharmacy = pharmacyInfoConfig?.filterNormalPharmacyList?.find((pharmacy) => pharmacy.no == pharmacyNo);
        const selectPharmacyInfo = !!filterExistPharmacy ? filterExistPharmacy : defaultPharmacyInfo;
        const initIndex = pharmacyInfoConfig?.filterNormalPharmacyList?.findIndex((t) => t.no == selectPharmacyInfo?.no);
        const selectIndex = await AbcDialog.showOptionsBottomSheet({
            title: "药品来源",
            options: pharmacyInfoConfig?.filterNormalPharmacyList.map((item) => `${item.name}`),
            initialSelectIndexes: initIndex > -1 ? new Set<number>([initIndex]) : undefined,
            crossAxisCount: 2,
            height: pxToDp(375),
        });
        if (!selectIndex || !selectIndex.length) return;
        const result = pharmacyInfoConfig?.filterNormalPharmacyList[selectIndex[0]];
        event.form.handleChangePharmacy(result);
        this.update();
    }

    @actionEvent(_EventModifyChargeItemRemark)
    private async *_mapEventModifyChargeItemRemark(event: _EventModifyChargeItemRemark): AsyncGenerator<State> {
        const { isDelete, formItem } = event;
        const acuNameList = new Map<String, AcuPointsItem>();
        const acuList: AcupunctureItem[] = [];

        if (isDelete) {
            //删除备注
            formItem.remark = undefined;
        } else {
            //修改备注
            const _value = await MedicineUsagePicker.showCustomUsageDialog({
                title: "备注",
                value: formItem.remark,
                enableDefaultToolBar: false,
                maxLength: 200, //修改穴位
                rightCustomization:
                    event.showAcuPointsIcon !== false
                        ? (callback) => (
                              <IconFontView
                                  style={{ marginHorizontal: Sizes.listHorizontalMargin }}
                                  name={"meridian"}
                                  size={Sizes.dp18}
                                  color={Colors.mainColor}
                                  onClick={async () => {
                                      if (!!formItem.remark) {
                                          let _remark = formItem.remark;
                                          const isExistAcuPoints = _remark.indexOf("[穴位]");
                                          if (isExistAcuPoints > -1) {
                                              const acuPointsCont = _remark.substring(isExistAcuPoints + 4);
                                              const acuPointsLastIndex = acuPointsCont.indexOf(";");
                                              const acuPointsResult =
                                                  acuPointsLastIndex == -1
                                                      ? acuPointsCont.substring(0)
                                                      : acuPointsCont.substring(0, acuPointsLastIndex);

                                              acuPointsResult
                                                  .replace(/，/gi, ",")
                                                  .replace("；", "")
                                                  .split("、")
                                                  .forEach((item) => {
                                                      acuList.push(
                                                          JsonMapper.deserialize(AcuPointsItem, {
                                                              name: item,
                                                          }) as AcupunctureItem
                                                      );
                                                  });
                                              _remark = _remark.replace("[穴位]", "");
                                              _remark = _remark.replace(acuPointsResult, "");
                                          }
                                          formItem.remark = _remark;
                                      }

                                      const pointInfo: AbcSet<AcupunctureItem> = await AcupunctureDialog.show(acuList);
                                      if (pointInfo) {
                                          const acuList: AcuPointsItem[] = [];
                                          pointInfo.forEach((item) => {
                                              if (acuNameList.has(item.name) && acuNameList.get(item.name)) {
                                                  acuList.push(acuNameList.get(item.name)!);
                                              } else {
                                                  const _item = item as AcuPointsItem;
                                                  acuList.push(_item);
                                              }
                                          });
                                          const acuListStr = acuList
                                              .map((f) => f.name)
                                              .join("、")
                                              .valueOf();
                                          formItem.remark = `[穴位]${acuListStr};`;
                                          callback(formItem.remark);
                                      }
                                  }}
                              />
                          )
                        : undefined,
            });

            if (_value) {
                formItem.remark = _value.name;
            }
        }

        this.update();
    }

    @actionEvent(_EventShowRegistrationDetail)
    private async *_mapEventShowRegistrationDetail(): AsyncGenerator<State> {
        const registrationDetail__ = this.innerState.detailData.registrationDetail__;
        if (!registrationDetail__) return;

        ExecuteRegistrationDetailDialog.show(registrationDetail__);
    }

    private async *_mapEventUpdateDoctor(event: _EventUpdateDoctor): AsyncGenerator<State> {
        const detailData = this.innerState.detailData;
        const therapySheet = (detailData.therapySheet = detailData.therapySheet ?? {});
        therapySheet.doctorId = event.id;
        therapySheet.doctorName = event.name;
        therapySheet.departmentId = event.departmentId;
        therapySheet.departmentName = event.departmentName;
        // 重新获取医生职称
        if (this.innerState.enableDoctorPractice && !!therapySheet.doctorId) {
            const result = await OutpatientAgent.getClinicEmployeesList(therapySheet.doctorId).catchIgnore();
            if (result) {
                this.innerState.currentDoctorDetailInfo = result;
            }
        }
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    private async *_mapEventUpdateDiagnosis(event: _EventUpdateDiagnosis): AsyncGenerator<State> {
        const detailData = this.innerState.detailData;
        const therapySheet = (detailData.therapySheet = detailData.therapySheet ?? {});
        therapySheet.diagnosis = event.diagnosis;
        //社保相关
        const diagnosisList = StringUtils.splitStringWithDiagnosis(event.diagnosis);
        therapySheet.diagnosisInfos = await ShebaoAgent.getDiseasesQueryList(
            diagnosisList.map((item) => {
                return { name: item, code: "", hint: "", type: 0 };
            }) ?? []
        )
            .then((rsp) => rsp.diagnosisInfos)
            .catchIgnore();
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    private async *_mapEventBack(/*event: _EventBack*/): AsyncGenerator<State> {
        if (this.innerState.isEditDraft) await this._saveLocalDraft();
        ABCNavigator.pop();
    }

    private async *_mapEventDelete(/*event: _EventDelete*/): AsyncGenerator<State> {
        const result = await showQueryDialog("确认删除本次执行开单吗？", "删除后不能恢复");
        if (result != DialogIndex.positive) return;
        if (this.innerState.isEditDraft) {
            await ExecuteInvoiceDraftManager.instance.removeDraft(this.innerState.detailData.localDraftId!);
            ABCNavigator.pop();
        } else {
            // desc: 调用接口删除线上数据(接口返回后 刷新上一个页面 同时返回到tab页面 )
            const status = await PatientOrderDataAgent.deleteExecuteInvoice(this.innerState.detailData.id!).catch(
                (error) => new ABCError(error)
            );
            if (status instanceof ABCError) {
                await Toast.show(errorToStr(status), { warning: true });
            } else if (status) {
                await Toast.show("删除成功", { success: true });
                ABCNavigator.popUntil(URLProtocols.EXECUTE_TAB);
            } else {
                await Toast.show("删除失败", { warning: true });
            }
        }
    }

    // 服用量
    @actionEvent(_EventChangeDosageCount)
    async *_mapEventChangeDosageCount(event: _EventChangeDosageCount): AsyncGenerator<State> {
        const chineseChargeForm = event.chargeForm;
        chineseChargeForm.doseCount__ = event.dosageCount;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    // 剂数、用法、剂量、频率
    @actionEvent(_EventChangeUsage)
    async *_mapEventChangeUsage(event: _EventChangeUsage): AsyncGenerator<State> {
        const newUsageInfo = event.usageInfo;
        const { chargeForm } = event;
        let newUsageDays = event.usageInfo.usageDays;
        //相等说明在用法组件里没有手动选择用服用天数, 重新计算服用天数
        if (newUsageInfo.usageDays === chargeForm.usageInfo?.usageDays) {
            if (event.chargeForm.usageInfo) Object.assign(event.chargeForm.usageInfo, event.usageInfo);
            ChargeUtils.refreshUsageDays(chargeForm);
            newUsageDays = chargeForm.usageInfo?.usageDays ?? newUsageDays;
        }
        if (!event.chargeForm.usageInfo) event.chargeForm.usageInfo = {};

        Object.assign(event.chargeForm.usageInfo, event.usageInfo);
        event.chargeForm.usageInfo.usageDays = newUsageDays;
        this.innerState.hasChanged = true;
        yield this.innerState.clone();
    }

    private async *_mapEventPreviewPrice(/*event: _EventPreviewPrice*/): AsyncGenerator<State> {
        const { calculateError } = this.innerState;
        if (calculateError) {
            this._calculateTrigger.next(0);
            await Toast.show("正在算费...");
            return;
        }

        const result = await SingleBargainDialog.show(
            this.innerState.detailData,
            true,
            true,
            "费用预览",
            !!this.innerState.employeesMeConfig?.employeeDataPermission?.cashier?.isCanSeeGoodsCostPrice
        );
        if (result != undefined) {
            this.innerState.detailData = result.chargeSheet;
            this.innerState.hasChanged = true;
            this._calculateTrigger.next(0);
        }
    }

    private async *_mapEventSubmit(/*event: _EventSubmit*/): AsyncGenerator<State> {
        if (!(await ChargeUtils.validateChargeDetailData(this.innerState.detailData))) {
            this.innerState.showErrorHint = true;
            yield this.innerState.clone();
            return;
        }
        const therapySheet = this.innerState.detailData.therapySheet;
        if (_.isEmpty(therapySheet?.diagnosis) || _.isEmpty(therapySheet?.doctorId)) {
            this.innerState.showErrorHint = true;
            yield this.innerState.clone();
            return;
        }

        const loadingDialog = new LoadingDialog("正在创建...");
        loadingDialog.show();
        this.innerState.detailData.sellerDepartmentId = this.innerState.detailData.departmentId;
        const rsp = await PatientOrderDataAgent.createExecuteInvoice(this.innerState.detailData).catch((error) => new ABCError(error)); // 创建执行单
        if (rsp instanceof ABCError) {
            await loadingDialog.fail("创建失败：" + errorSummary(rsp));
            return;
        }

        await loadingDialog.hide();
        this.innerState.hasChanged = false;
        this.innerState.detailData.localDraftId &&
            (await ExecuteInvoiceDraftManager.instance.removeDraft(this.innerState.detailData.localDraftId));

        ABCNavigator.navigateToPage(<ExecuteInvoicePage orderId={rsp.id} />, { replace: true }).then();
    }

    private async _saveLocalDraft() {
        const innerState = this.innerState;

        if (innerState.detailData?.isLocalDraft && this.innerState.hasChanged) {
            await ExecuteInvoiceDraftManager.instance.saveDraft(innerState.detailData);
        }
    }

    @actionEvent(_EventModifyProductEmployee)
    private async *_mapEventModifyProductEmployee(): AsyncGenerator<State> {
        const goodsInfoList: GoodsInfo[] = [];
        const doctorList: AbcMap<string, { id?: string; name?: string; departmentId?: string }> = new AbcMap<
                string,
                { id?: string; name?: string; departmentId?: string }
            >(),
            nurseList: AbcMap<string, { id?: string; name?: string }> = new AbcMap<string, { id?: string; name?: string }>();

        this.innerState.treatmentChargeForm.map((item) => {
            goodsInfoList.push(item.goodsInfo);
            nurseList.set(item.goodsInfo.scrollKey, { id: item.nurseId, name: item.nurseName });
            doctorList.set(item.goodsInfo.scrollKey, { id: item.doctorId, name: item.doctorName, departmentId: item.departmentId });
        });

        const rsp = await ProductAddExecutorDialog.show({ goodsInfoList: goodsInfoList, doctorList, nurseList });

        if (!!rsp) {
            const currentDoctorList = rsp.doctorList;
            const currentNurseList = rsp.nurseList;
            this.innerState.detailData.getChargeForm(ChargeSourceFormType.treatment)?.chargeFormItems?.forEach((chargeItem) => {
                this.innerState.treatmentChargeForm.forEach((item) => {
                    if (chargeItem.goodsInfo.scrollKey != item.goodsInfo.scrollKey) return;
                    if (currentDoctorList.has(item.goodsInfo.scrollKey)) {
                        const currentDoctor = currentDoctorList.get(item.goodsInfo.scrollKey);
                        chargeItem.doctorId = currentDoctor?.id;
                        chargeItem.doctorName = currentDoctor?.name;
                        chargeItem.departmentId = currentDoctor?.departmentId;
                        chargeItem.departmentName = currentDoctor?.departmentName;
                    }
                    if (currentNurseList.has(item.goodsInfo.scrollKey)) {
                        const currentNurse = currentNurseList.get(item.goodsInfo.scrollKey);
                        chargeItem.nurseId = currentNurse?.id;
                        chargeItem.nurseName = currentNurse?.name;
                    }
                });
            });

            this.update();
        }
    }

    @actionEvent(_EventSaveEditDetail)
    private async *_mapEventSaveEditDetail(): AsyncGenerator<State> {
        const _showLoadingDialog = new LoadingDialog();
        _showLoadingDialog.show();
        const status = await PatientOrderDataAgent.modifyExecuteInvoice(this.innerState.detailData!).catch((error) => new ABCError(error));
        await _showLoadingDialog.hide();
        if (status instanceof ABCError) {
            await Toast.show(errorToStr(status), { warning: true });
        } else if (status) {
            await Toast.show("保存成功", { success: true });
            ABCNavigator.pop();
        } else {
            await Toast.show("保存失败", { warning: true });
        }
    }

    private async *_mapEventNewAddTreatmentItems(/*event: _EventAddTreatmentItems*/): AsyncGenerator<State> {
        const result = ChargeUtils.toDirectChargeMedicineAddInfo(this.innerState.detailData!, false);
        const params = new DirectChargeMedicineInvokeParams();
        params.medicineInfo = result;
        params.onDeleteItems = async (goodsList) => {
            return ChargeItemModifyUtils.checkDeleteChargeItems(undefined, goodsList, this.innerState.detailData);
        };

        params.onPreSave = async (goodsList) => {
            return ChargeItemModifyUtils.checkModifyChargeItemsUnitCount(undefined, goodsList, this.innerState.detailData);
        };

        params.showChineseUsage = false;
        params.goodsTypes = this.innerState.canOpenItemGoodsTypes.map((t) => t.type);
        const showScanIcon = params.goodsTypes.includes(1);
        const medicines = await showBottomPanel<DirectChargeMedicineAddInfo | undefined>(
            <ChargeAddMedicinePage
                params={params}
                nurseFilterInfo={{
                    openGoodsTypes: this.innerState.nurseStationConfig?.canOpenGoodsTypes,
                    fromNursePage: true,
                }}
                showScanIcon={showScanIcon}
            />
        );
        this.innerState.needContinueAddMedicine = !!medicines;
        this.innerState.hasChanged = true;
        if (!medicines) return;
        const goodsInfo = medicines.goods[0];
        if (!isUndefined(goodsInfo.antibiotic) && this.innerState.allowAntibiotic) {
            if (!this.innerState.allowAntibioticList?.includes(goodsInfo.antibiotic ?? 0)) {
                return showConfirmDialog(
                    "提示",
                    `【${goodsInfo.displayName}】为${goodsInfo._antibioticDisplay}药物，当前医生不具备使用权限`
                );
            }
        }

        let toastShow = false;
        const copyMedicineGoods = _.clone(medicines.goods);
        const oldChargeFormItems: ChargeFormItem[] = [];
        this.innerState.detailData?.chargeForms?.forEach((form) => {
            oldChargeFormItems.push(...(form.chargeFormItems ?? []));
        });

        const newAddGoods: GoodsInfo[] = []; // 筛选出最新添加的项目，用于聚焦到该输入框
        let newAddGoodsInfo = copyMedicineGoods[Math.max(copyMedicineGoods.length - 1, 0)];
        newAddGoodsInfo =
            (oldChargeFormItems.find((sub) => newAddGoodsInfo.id == sub.productId)?.productInfo as GoodsInfo) ?? newAddGoodsInfo;

        for (const item of medicines.goods) {
            let flag = false;
            for (const sub of oldChargeFormItems) {
                if (item.id == sub.productId) {
                    flag = true;
                    toastShow = true;
                    break;
                }
            }
            if (!flag) {
                toastShow = false;
                newAddGoods.push(item);
            }
        }

        if (toastShow) {
            await Toast.show(`${newAddGoodsInfo.medicineCadn ?? newAddGoodsInfo.name}已添加`, { warning: true });
        }
        medicines.goods = newAddGoods;
        this.innerState.currentFocusItem = newAddGoodsInfo;
        this.innerState.groups = medicines?.goods;

        ChargeUtils.addMedicinesToChargeInvoice({
            detailData: this.innerState.detailData,
            medicines,
        });

        //药房的修改信息覆盖到原始数据上
        this.innerState.detailData?.chargeForms?.forEach((form) => {
            if (form.isChinesePrescription) {
                const oldForm = oldChargeFormItems.find((item) => item.isChineseMedicine);
                if (!oldForm) return;
                form.pharmacyNo = oldForm.pharmacyNo;
                form.pharmacyType = oldForm.pharmacyType;
                form.pharmacyName = oldForm.pharmacyName;
            } else {
                form.chargeFormItems?.forEach((formItem) => {
                    oldChargeFormItems.forEach((oldForm) => {
                        if (formItem.goodsInfo.id == oldForm.goodsInfo.id) {
                            formItem.pharmacyNo = oldForm.pharmacyNo;
                            formItem.pharmacyType = oldForm.pharmacyType;
                            formItem.pharmacyName = oldForm.pharmacyName;
                        }
                    });
                });
            }
        });

        this._calculateTrigger.next(0);
        yield this.innerState.clone();
        yield ScrollToFocusItemState.fromState(this.innerState);
    }
}
